// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase1-DataAccess"
//   Timestamp: "2024-12-19T10:45:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-High-Cohesion"
//   Quality_Check: "High-performance data access using ADO.NET with proper resource management."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using AlarmAnalysis.Models;

namespace AlarmAnalysis.DataAccess
{
    /// <summary>
    /// 高性能报警数据读取器，使用ADO.NET和SqlDataReader实现流式读取
    /// </summary>
    public class AlarmDataReader : IDisposable
    {
        private readonly string _connectionString;
        private SqlConnection _connection;
        private bool _disposed = false;
        
        #region 构造函数和初始化
        
        /// <summary>
        /// 默认构造函数，从配置文件读取连接字符串
        /// </summary>
        public AlarmDataReader()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["AlarmDatabase"]?.ConnectionString;
            if (string.IsNullOrWhiteSpace(_connectionString))
            {
                throw new ConfigurationErrorsException("未找到名为'AlarmDatabase'的连接字符串配置");
            }
        }
        
        /// <summary>
        /// 带连接字符串的构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        public AlarmDataReader(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
                throw new ArgumentException("连接字符串不能为空", nameof(connectionString));
                
            _connectionString = connectionString;
        }
        
        #endregion
        
        #region 数据读取方法
        
        /// <summary>
        /// 读取指定时间范围内的所有报警事件
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="tableName">报警历史表名，默认为AlarmHistory</param>
        /// <returns>按时间排序的报警事件列表</returns>
        public List<AlarmEvent> ReadAlarmEvents(DateTime startTime, DateTime endTime, string tableName = "AlarmHistory")
        {
            var alarmEvents = new List<AlarmEvent>();
            
            try
            {
                using (_connection = new SqlConnection(_connectionString))
                {
                    _connection.Open();
                    
                    string sql = $@"
                        SELECT 
                            EventId,
                            EventType,
                            SourceNode,
                            SourceName,
                            EventDateTime,
                            EventDateTimeUtc,
                            EventMessage,
                            EventDetails,
                            EventState,
                            Severity,
                            EventComment,
                            UserName,
                            EventDuration,
                            EventOccurence,
                            EventSequence,
                            RedundancySyncTime
                        FROM {tableName}
                        WHERE EventDateTime >= @StartTime 
                        AND EventDateTime <= @EndTime
                        ORDER BY EventDateTime ASC";
                    
                    using (var command = new SqlCommand(sql, _connection))
                    {
                        // 添加参数防止SQL注入
                        command.Parameters.Add("@StartTime", SqlDbType.DateTime).Value = startTime;
                        command.Parameters.Add("@EndTime", SqlDbType.DateTime).Value = endTime;
                        
                        // 设置命令超时时间（30秒）
                        command.CommandTimeout = 30;
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var alarmEvent = MapReaderToAlarmEvent(reader);
                                alarmEvents.Add(alarmEvent);
                            }
                        }
                    }
                }
                
                return alarmEvents;
            }
            catch (SqlException sqlEx)
            {
                throw new DataException($"数据库查询失败: {sqlEx.Message}", sqlEx);
            }
            catch (Exception ex)
            {
                throw new DataException($"读取报警数据时发生错误: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 异步读取指定时间范围内的所有报警事件
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="tableName">报警历史表名</param>
        /// <returns>按时间排序的报警事件列表</returns>
        public async Task<List<AlarmEvent>> ReadAlarmEventsAsync(DateTime startTime, DateTime endTime, string tableName = "AlarmHistory")
        {
            var alarmEvents = new List<AlarmEvent>();
            
            try
            {
                using (_connection = new SqlConnection(_connectionString))
                {
                    await _connection.OpenAsync();
                    
                    string sql = $@"
                        SELECT 
                            EventId,
                            EventType,
                            SourceNode,
                            SourceName,
                            EventDateTime,
                            EventDateTimeUtc,
                            EventMessage,
                            EventDetails,
                            EventState,
                            Severity,
                            EventComment,
                            UserName,
                            EventDuration,
                            EventOccurence,
                            EventSequence,
                            RedundancySyncTime
                        FROM {tableName}
                        WHERE EventDateTime >= @StartTime 
                        AND EventDateTime <= @EndTime
                        ORDER BY EventDateTime ASC";
                    
                    using (var command = new SqlCommand(sql, _connection))
                    {
                        command.Parameters.Add("@StartTime", SqlDbType.DateTime).Value = startTime;
                        command.Parameters.Add("@EndTime", SqlDbType.DateTime).Value = endTime;
                        command.CommandTimeout = 30;
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var alarmEvent = MapReaderToAlarmEvent(reader);
                                alarmEvents.Add(alarmEvent);
                            }
                        }
                    }
                }
                
                return alarmEvents;
            }
            catch (SqlException sqlEx)
            {
                throw new DataException($"数据库查询失败: {sqlEx.Message}", sqlEx);
            }
            catch (Exception ex)
            {
                throw new DataException($"读取报警数据时发生错误: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 流式读取报警事件，适用于大数据集处理
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="onDataReceived">数据接收回调</param>
        /// <param name="batchSize">批处理大小</param>
        /// <param name="tableName">表名</param>
        public void ReadAlarmEventsStream(DateTime startTime, DateTime endTime, 
            Action<List<AlarmEvent>> onDataReceived, int batchSize = 1000, string tableName = "AlarmHistory")
        {
            if (onDataReceived == null)
                throw new ArgumentNullException(nameof(onDataReceived));
                
            try
            {
                using (_connection = new SqlConnection(_connectionString))
                {
                    _connection.Open();
                    
                    string sql = $@"
                        SELECT 
                            EventId,
                            EventType,
                            SourceNode,
                            SourceName,
                            EventDateTime,
                            EventDateTimeUtc,
                            EventMessage,
                            EventDetails,
                            EventState,
                            Severity,
                            EventComment,
                            UserName,
                            EventDuration,
                            EventOccurence,
                            EventSequence,
                            RedundancySyncTime
                        FROM {tableName}
                        WHERE EventDateTime >= @StartTime 
                        AND EventDateTime <= @EndTime
                        ORDER BY EventDateTime ASC";
                    
                    using (var command = new SqlCommand(sql, _connection))
                    {
                        command.Parameters.Add("@StartTime", SqlDbType.DateTime).Value = startTime;
                        command.Parameters.Add("@EndTime", SqlDbType.DateTime).Value = endTime;
                        command.CommandTimeout = 60; // 增加超时时间用于大数据集
                        
                        using (var reader = command.ExecuteReader())
                        {
                            var batch = new List<AlarmEvent>(batchSize);
                            
                            while (reader.Read())
                            {
                                var alarmEvent = MapReaderToAlarmEvent(reader);
                                batch.Add(alarmEvent);
                                
                                if (batch.Count >= batchSize)
                                {
                                    onDataReceived(batch);
                                    batch.Clear();
                                }
                            }
                            
                            // 处理剩余的数据
                            if (batch.Count > 0)
                            {
                                onDataReceived(batch);
                            }
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                throw new DataException($"流式读取数据失败: {sqlEx.Message}", sqlEx);
            }
            catch (Exception ex)
            {
                throw new DataException($"流式读取报警数据时发生错误: {ex.Message}", ex);
            }
        }
        
        #endregion
        
        #region 数据库连接测试
        
        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public bool TestConnection()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 异步测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 获取指定表的记录总数
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="startTime">开始时间（可选）</param>
        /// <param name="endTime">结束时间（可选）</param>
        /// <returns>记录总数</returns>
        public long GetRecordCount(string tableName = "AlarmHistory", DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    string sql = $"SELECT COUNT(*) FROM {tableName}";
                    var parameters = new List<SqlParameter>();
                    
                    if (startTime.HasValue && endTime.HasValue)
                    {
                        sql += " WHERE EventDateTime >= @StartTime AND EventDateTime <= @EndTime";
                        parameters.Add(new SqlParameter("@StartTime", startTime.Value));
                        parameters.Add(new SqlParameter("@EndTime", endTime.Value));
                    }
                    
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                        var result = command.ExecuteScalar();
                        return Convert.ToInt64(result);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new DataException($"获取记录总数失败: {ex.Message}", ex);
            }
        }
        
        #endregion
        
        #region 私有辅助方法
        
        /// <summary>
        /// 将SqlDataReader的当前行映射到AlarmEvent对象
        /// </summary>
        /// <param name="reader">数据读取器</param>
        /// <returns>AlarmEvent对象</returns>
        private static AlarmEvent MapReaderToAlarmEvent(SqlDataReader reader)
        {
            var alarmEvent = new AlarmEvent
            {
                EventId = reader["EventId"] == DBNull.Value ? string.Empty : reader["EventId"].ToString(),
                EventType = reader["EventType"] == DBNull.Value ? string.Empty : reader["EventType"].ToString(),
                SourceNode = reader["SourceNode"] == DBNull.Value ? string.Empty : reader["SourceNode"].ToString(),
                SourceName = reader["SourceName"].ToString(),
                EventDateTime = Convert.ToDateTime(reader["EventDateTime"]),
                EventDateTimeUtc = reader["EventDateTimeUtc"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["EventDateTimeUtc"]),
                EventMessage = reader["EventMessage"].ToString(),
                EventDetails = reader["EventDetails"] == DBNull.Value ? string.Empty : reader["EventDetails"].ToString(),
                EventState = reader["EventState"].ToString(),
                Severity = reader["Severity"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["Severity"]),
                EventComment = reader["EventComment"] == DBNull.Value ? string.Empty : reader["EventComment"].ToString(),
                UserName = reader["UserName"] == DBNull.Value ? string.Empty : reader["UserName"].ToString(),
                EventDuration = reader["EventDuration"] == DBNull.Value ? 0.0 : Convert.ToDouble(reader["EventDuration"]),
                EventOccurence = reader["EventOccurence"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["EventOccurence"]),
                EventSequence = reader["EventSequence"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["EventSequence"]),
                RedundancySyncTime = reader["RedundancySyncTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["RedundancySyncTime"])
            };
            
            // 触发SourceName解析
            alarmEvent.SetSourceName(alarmEvent.SourceName);
            
            return alarmEvent;
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _connection?.Dispose();
                _disposed = true;
            }
        }
        
        /// <summary>
        /// 析构函数
        /// </summary>
        ~AlarmDataReader()
        {
            Dispose(false);
        }
        
        #endregion
    }
}
// {{END_MODIFICATIONS}}
