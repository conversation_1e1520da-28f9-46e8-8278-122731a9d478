// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase3-AdvancedAnalysis"
//   Timestamp: "2025-01-24T12:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), DRY, YAGNI"
//   Quality_Check: "高级报警分析器，实现生命周期追踪、KPI计算和抖动检测。"
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using AlarmAnalysis.Common;
using AlarmAnalysis.Models;
using log4net;

namespace AlarmAnalysis.Analysis
{
    /// <summary>
    /// 高级报警分析器 - Phase 3功能实现
    /// 提供报警生命周期分析、响应KPI计算和抖动检测
    /// </summary>
    public class AdvancedAlarmAnalyzer : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(AdvancedAlarmAnalyzer));
        
        private readonly int _flutterTimeWindowSeconds;
        private readonly int _flutterThreshold;
        private bool _disposed = false;
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数，从配置文件读取参数
        /// </summary>
        public AdvancedAlarmAnalyzer()
        {
            _flutterTimeWindowSeconds = ConfigurationHelper.GetIntValue("FlutterTimeWindowSeconds", 60);
            _flutterThreshold = ConfigurationHelper.GetIntValue("FlutterThreshold", 3);
            
            _logger.Info($"AdvancedAlarmAnalyzer初始化完成 - 抖动检测窗口: {_flutterTimeWindowSeconds}秒, 阈值: {_flutterThreshold}次");
        }
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="flutterTimeWindowSeconds">抖动检测时间窗口（秒）</param>
        /// <param name="flutterThreshold">抖动检测阈值</param>
        public AdvancedAlarmAnalyzer(int flutterTimeWindowSeconds, int flutterThreshold)
        {
            _flutterTimeWindowSeconds = flutterTimeWindowSeconds > 0 ? flutterTimeWindowSeconds : 60;
            _flutterThreshold = flutterThreshold > 0 ? flutterThreshold : 3;
            
            _logger.Info($"AdvancedAlarmAnalyzer初始化完成 - 抖动检测窗口: {_flutterTimeWindowSeconds}秒, 阈值: {_flutterThreshold}次");
        }
        
        #endregion
        
        #region 报警生命周期重构
        
        /// <summary>
        /// 重构报警生命周期，追踪每个报警实例的完整状态变迁
        /// </summary>
        /// <param name="alarmEvents">按时间排序的报警事件列表</param>
        /// <returns>报警生命周期字典</returns>
        public Dictionary<string, AlarmLifecycle> ReconstructAlarmLifecycles(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || alarmEvents.Count == 0)
            {
                _logger.Warn("输入的报警事件列表为空，返回空的生命周期字典");
                return new Dictionary<string, AlarmLifecycle>();
            }
            
            _logger.Info($"开始重构报警生命周期，处理 {alarmEvents.Count} 个报警事件");
            
            try
            {
                var lifecycles = new Dictionary<string, AlarmLifecycle>();
                int processedCount = 0;
                
                foreach (var alarmEvent in alarmEvents.OrderBy(e => e.EventDateTime))
                {
                    processedCount++;
                    
                    // 生成唯一标识符：SourceName + EventMessage + 初始触发时间戳
                    string lifecycleKey = GenerateLifecycleKey(alarmEvent);
                    
                    if (!lifecycles.ContainsKey(lifecycleKey))
                    {
                        // 创建新的生命周期实例
                        lifecycles[lifecycleKey] = new AlarmLifecycle
                        {
                            LifecycleId = lifecycleKey,
                            SourceName = alarmEvent.SourceName,
                            Station = alarmEvent.Station,
                            Device = alarmEvent.Device,
                            EventMessage = alarmEvent.EventMessage,
                            InitialTriggerTime = alarmEvent.EventDateTime,
                            StateTransitions = new List<AlarmStateTransition>()
                        };
                        
                        _logger.Debug($"创建新的报警生命周期: {lifecycleKey}");
                    }
                    
                    // 添加状态转换记录
                    var lifecycle = lifecycles[lifecycleKey];
                    lifecycle.StateTransitions.Add(new AlarmStateTransition
                    {
                        EventDateTime = alarmEvent.EventDateTime,
                        EventState = alarmEvent.EventState,
                        EventId = alarmEvent.EventId,
                        Severity = alarmEvent.Severity,
                        UserName = alarmEvent.UserName,
                        EventSequence = alarmEvent.EventSequence
                    });
                    
                    // 更新生命周期状态
                    UpdateLifecycleStatus(lifecycle, alarmEvent);
                    
                    // 进度日志
                    if (processedCount % 1000 == 0)
                    {
                        _logger.Debug($"已处理 {processedCount}/{alarmEvents.Count} 个报警事件");
                    }
                }
                
                _logger.Info($"报警生命周期重构完成，共生成 {lifecycles.Count} 个生命周期实例");
                return lifecycles;
            }
            catch (Exception ex)
            {
                _logger.Error($"报警生命周期重构失败: {ex.Message}", ex);
                throw new AnalysisException($"报警生命周期重构失败: {ex.Message}", ex, "LifecycleReconstruction", alarmEvents.Count);
            }
        }
        
        /// <summary>
        /// 生成生命周期唯一标识符
        /// </summary>
        /// <param name="alarmEvent">报警事件</param>
        /// <returns>唯一标识符</returns>
        private string GenerateLifecycleKey(AlarmEvent alarmEvent)
        {
            // 使用SourceName + EventMessage + 时间戳（精确到分钟）作为唯一标识
            // 这样可以区分同一设备同一类型但不同时间段的报警实例
            var timeKey = alarmEvent.EventDateTime.ToString("yyyyMMddHHmm");
            return $"{alarmEvent.SourceName}|{alarmEvent.EventMessage}|{timeKey}";
        }
        
        /// <summary>
        /// 更新生命周期状态
        /// </summary>
        /// <param name="lifecycle">生命周期实例</param>
        /// <param name="alarmEvent">当前报警事件</param>
        private void UpdateLifecycleStatus(AlarmLifecycle lifecycle, AlarmEvent alarmEvent)
        {
            try
            {
                // 更新最后更新时间
                lifecycle.LastUpdateTime = alarmEvent.EventDateTime;
                lifecycle.CurrentState = alarmEvent.EventState;
                
                // 根据状态更新关键时间点
                if (alarmEvent.IsActive && !lifecycle.FirstActiveTime.HasValue)
                {
                    lifecycle.FirstActiveTime = alarmEvent.EventDateTime;
                }
                
                if (alarmEvent.EventState.Contains("Acknowledged") && !lifecycle.AcknowledgedTime.HasValue)
                {
                    lifecycle.AcknowledgedTime = alarmEvent.EventDateTime;
                    lifecycle.AcknowledgedBy = alarmEvent.UserName;
                }
                
                if (alarmEvent.IsInactive && !lifecycle.ResolvedTime.HasValue)
                {
                    lifecycle.ResolvedTime = alarmEvent.EventDateTime;
                }
                
                // 计算持续时间
                if (lifecycle.FirstActiveTime.HasValue)
                {
                    lifecycle.TotalDuration = lifecycle.LastUpdateTime - lifecycle.FirstActiveTime.Value;
                }
                
                // 更新状态标志
                lifecycle.IsCurrentlyActive = alarmEvent.IsActive;
                lifecycle.IsAcknowledged = alarmEvent.EventState.Contains("Acknowledged");
                lifecycle.IsResolved = alarmEvent.IsInactive;
            }
            catch (Exception ex)
            {
                _logger.Warn($"更新生命周期状态时发生异常: {ex.Message}, LifecycleId: {lifecycle.LifecycleId}");
            }
        }
        
        #endregion

        #region 响应KPI计算

        /// <summary>
        /// 计算响应KPI指标（TTA和TTR）
        /// </summary>
        /// <param name="lifecycles">报警生命周期字典</param>
        /// <returns>响应KPI结果</returns>
        public ResponseKPIResult CalculateResponseKPIs(Dictionary<string, AlarmLifecycle> lifecycles)
        {
            if (lifecycles == null || lifecycles.Count == 0)
            {
                _logger.Warn("输入的生命周期字典为空，返回空的KPI结果");
                return new ResponseKPIResult();
            }

            _logger.Info($"开始计算响应KPI，处理 {lifecycles.Count} 个生命周期实例");

            try
            {
                var result = new ResponseKPIResult();
                var ttaValues = new List<double>();
                var ttrValues = new List<double>();

                foreach (var lifecycle in lifecycles.Values)
                {
                    // 收集TTA数据
                    if (lifecycle.TimeToAcknowledge.HasValue)
                    {
                        ttaValues.Add(lifecycle.TimeToAcknowledge.Value.TotalMinutes);
                    }

                    // 收集TTR数据
                    if (lifecycle.TimeToResolve.HasValue)
                    {
                        ttrValues.Add(lifecycle.TimeToResolve.Value.TotalMinutes);
                    }
                }

                // 计算TTA统计
                result.TTAStatistics = CalculateTimeStatistics(ttaValues, "TTA");

                // 计算TTR统计
                result.TTRStatistics = CalculateTimeStatistics(ttrValues, "TTR");

                // 计算总体统计
                result.TotalLifecycles = lifecycles.Count;
                result.AcknowledgedCount = lifecycles.Values.Count(l => l.IsAcknowledged);
                result.ResolvedCount = lifecycles.Values.Count(l => l.IsResolved);
                result.ActiveCount = lifecycles.Values.Count(l => l.IsCurrentlyActive);

                result.AcknowledgmentRate = result.TotalLifecycles > 0 ?
                    (double)result.AcknowledgedCount / result.TotalLifecycles * 100 : 0;
                result.ResolutionRate = result.TotalLifecycles > 0 ?
                    (double)result.ResolvedCount / result.TotalLifecycles * 100 : 0;

                _logger.Info($"响应KPI计算完成 - TTA样本: {ttaValues.Count}, TTR样本: {ttrValues.Count}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"响应KPI计算失败: {ex.Message}", ex);
                throw new AnalysisException($"响应KPI计算失败: {ex.Message}", ex, "ResponseKPICalculation", lifecycles.Count);
            }
        }

        /// <summary>
        /// 计算时间统计指标
        /// </summary>
        /// <param name="values">时间值列表（分钟）</param>
        /// <param name="metricName">指标名称</param>
        /// <returns>时间统计结果</returns>
        private TimeStatistics CalculateTimeStatistics(List<double> values, string metricName)
        {
            var stats = new TimeStatistics { MetricName = metricName };

            if (values == null || values.Count == 0)
            {
                _logger.Debug($"{metricName} 统计：无有效数据");
                return stats;
            }

            var sortedValues = values.OrderBy(v => v).ToList();

            stats.SampleCount = values.Count;
            stats.MinValue = TimeSpan.FromMinutes(sortedValues.First());
            stats.MaxValue = TimeSpan.FromMinutes(sortedValues.Last());
            stats.AverageValue = TimeSpan.FromMinutes(values.Average());

            // 计算中位数
            if (sortedValues.Count % 2 == 0)
            {
                var mid1 = sortedValues[sortedValues.Count / 2 - 1];
                var mid2 = sortedValues[sortedValues.Count / 2];
                stats.MedianValue = TimeSpan.FromMinutes((mid1 + mid2) / 2);
            }
            else
            {
                stats.MedianValue = TimeSpan.FromMinutes(sortedValues[sortedValues.Count / 2]);
            }

            // 计算标准差
            var variance = values.Select(v => Math.Pow(v - values.Average(), 2)).Average();
            stats.StandardDeviation = TimeSpan.FromMinutes(Math.Sqrt(variance));

            // 计算百分位数
            stats.Percentile95 = TimeSpan.FromMinutes(GetPercentile(sortedValues, 0.95));
            stats.Percentile99 = TimeSpan.FromMinutes(GetPercentile(sortedValues, 0.99));

            _logger.Debug($"{metricName} 统计完成 - 样本数: {stats.SampleCount}, 平均值: {stats.AverageValue.TotalMinutes:F2}分钟");

            return stats;
        }

        /// <summary>
        /// 计算百分位数
        /// </summary>
        /// <param name="sortedValues">已排序的数值列表</param>
        /// <param name="percentile">百分位数（0-1）</param>
        /// <returns>百分位数值</returns>
        private double GetPercentile(List<double> sortedValues, double percentile)
        {
            if (sortedValues.Count == 0) return 0;
            if (sortedValues.Count == 1) return sortedValues[0];

            double index = percentile * (sortedValues.Count - 1);
            int lowerIndex = (int)Math.Floor(index);
            int upperIndex = (int)Math.Ceiling(index);

            if (lowerIndex == upperIndex)
            {
                return sortedValues[lowerIndex];
            }

            double weight = index - lowerIndex;
            return sortedValues[lowerIndex] * (1 - weight) + sortedValues[upperIndex] * weight;
        }

        #endregion

        #region 抖动与瞬时报警检测

        /// <summary>
        /// 检测抖动报警
        /// </summary>
        /// <param name="alarmEvents">按时间排序的报警事件列表</param>
        /// <returns>抖动报警检测结果</returns>
        public List<FlutterAlarmResult> DetectFlutterAlarms(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || alarmEvents.Count == 0)
            {
                _logger.Warn("输入的报警事件列表为空，返回空的抖动检测结果");
                return new List<FlutterAlarmResult>();
            }

            _logger.Info($"开始抖动报警检测，处理 {alarmEvents.Count} 个报警事件，时间窗口: {_flutterTimeWindowSeconds}秒，阈值: {_flutterThreshold}次");

            try
            {
                var flutterResults = new List<FlutterAlarmResult>();
                var sortedEvents = alarmEvents.OrderBy(e => e.EventDateTime).ToList();

                // 按SourceName + EventMessage分组
                var groupedEvents = sortedEvents
                    .GroupBy(e => $"{e.SourceName}|{e.EventMessage}")
                    .Where(g => g.Count() >= _flutterThreshold)
                    .ToList();

                foreach (var group in groupedEvents)
                {
                    var events = group.OrderBy(e => e.EventDateTime).ToList();
                    var flutterInstances = DetectFlutterInGroup(events);
                    flutterResults.AddRange(flutterInstances);
                }

                _logger.Info($"抖动报警检测完成，发现 {flutterResults.Count} 个抖动实例");
                return flutterResults;
            }
            catch (Exception ex)
            {
                _logger.Error($"抖动报警检测失败: {ex.Message}", ex);
                throw new AnalysisException($"抖动报警检测失败: {ex.Message}", ex, "FlutterDetection", alarmEvents.Count);
            }
        }

        /// <summary>
        /// 在单个分组中检测抖动
        /// </summary>
        /// <param name="events">同一类型的报警事件列表</param>
        /// <returns>抖动实例列表</returns>
        private List<FlutterAlarmResult> DetectFlutterInGroup(List<AlarmEvent> events)
        {
            var flutterResults = new List<FlutterAlarmResult>();
            var windowSize = TimeSpan.FromSeconds(_flutterTimeWindowSeconds);

            // 使用滑动窗口算法
            for (int i = 0; i < events.Count; i++)
            {
                var windowStart = events[i].EventDateTime;
                var windowEnd = windowStart.Add(windowSize);

                // 收集窗口内的激活事件
                var activeEventsInWindow = events
                    .Where(e => e.EventDateTime >= windowStart &&
                               e.EventDateTime <= windowEnd &&
                               e.IsActive)
                    .ToList();

                if (activeEventsInWindow.Count >= _flutterThreshold)
                {
                    // 检查是否已经记录过这个时间段的抖动
                    bool alreadyRecorded = flutterResults.Any(f =>
                        Math.Abs((f.WindowStartTime - windowStart).TotalSeconds) < _flutterTimeWindowSeconds / 2);

                    if (!alreadyRecorded)
                    {
                        var flutterResult = new FlutterAlarmResult
                        {
                            SourceName = events[i].SourceName,
                            Station = events[i].Station,
                            Device = events[i].Device,
                            EventMessage = events[i].EventMessage,
                            WindowStartTime = windowStart,
                            WindowEndTime = windowEnd,
                            WindowSize = windowSize,
                            ActivationCount = activeEventsInWindow.Count,
                            FlutterEvents = activeEventsInWindow.Select(e => new FlutterEventInfo
                            {
                                EventDateTime = e.EventDateTime,
                                EventId = e.EventId,
                                EventState = e.EventState,
                                Severity = e.Severity
                            }).ToList()
                        };

                        flutterResults.Add(flutterResult);

                        _logger.Debug($"检测到抖动报警: {flutterResult.SourceName} - {flutterResult.EventMessage}, " +
                                    $"窗口: {windowStart:HH:mm:ss} - {windowEnd:HH:mm:ss}, 激活次数: {flutterResult.ActivationCount}");
                    }
                }
            }

            return flutterResults;
        }

        /// <summary>
        /// 检测瞬时报警
        /// </summary>
        /// <param name="lifecycles">报警生命周期字典</param>
        /// <returns>瞬时报警列表</returns>
        public List<TransientAlarmResult> DetectTransientAlarms(Dictionary<string, AlarmLifecycle> lifecycles)
        {
            if (lifecycles == null || lifecycles.Count == 0)
            {
                _logger.Warn("输入的生命周期字典为空，返回空的瞬时报警结果");
                return new List<TransientAlarmResult>();
            }

            _logger.Info($"开始瞬时报警检测，处理 {lifecycles.Count} 个生命周期实例");

            try
            {
                var transientResults = new List<TransientAlarmResult>();

                foreach (var lifecycle in lifecycles.Values)
                {
                    if (lifecycle.IsTransientAlarm)
                    {
                        var transientResult = new TransientAlarmResult
                        {
                            LifecycleId = lifecycle.LifecycleId,
                            SourceName = lifecycle.SourceName,
                            Station = lifecycle.Station,
                            Device = lifecycle.Device,
                            EventMessage = lifecycle.EventMessage,
                            ActivationTime = lifecycle.FirstActiveTime ?? lifecycle.InitialTriggerTime,
                            DeactivationTime = lifecycle.ResolvedTime ?? lifecycle.LastUpdateTime,
                            Duration = lifecycle.TimeToResolve ?? lifecycle.TotalDuration,
                            StateTransitions = lifecycle.StateTransitions.Select(t => new TransientStateInfo
                            {
                                EventDateTime = t.EventDateTime,
                                EventState = t.EventState,
                                EventId = t.EventId
                            }).ToList()
                        };

                        transientResults.Add(transientResult);

                        _logger.Debug($"检测到瞬时报警: {transientResult.SourceName} - {transientResult.EventMessage}, " +
                                    $"持续时间: {transientResult.Duration.TotalMinutes:F2}分钟");
                    }
                }

                _logger.Info($"瞬时报警检测完成，发现 {transientResults.Count} 个瞬时报警");
                return transientResults;
            }
            catch (Exception ex)
            {
                _logger.Error($"瞬时报警检测失败: {ex.Message}", ex);
                throw new AnalysisException($"瞬时报警检测失败: {ex.Message}", ex, "TransientDetection", lifecycles.Count);
            }
        }

        #endregion

        #region IDisposable实现
        
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _logger.Info("AdvancedAlarmAnalyzer资源释放完成");
                }
                _disposed = true;
            }
        }
        
        #endregion
    }

    #region 数据模型定义

    /// <summary>
    /// 报警生命周期数据模型
    /// </summary>
    public class AlarmLifecycle
    {
        /// <summary>
        /// 生命周期唯一标识符
        /// </summary>
        public string LifecycleId { get; set; }

        /// <summary>
        /// 报警源名称
        /// </summary>
        public string SourceName { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string Station { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Device { get; set; }

        /// <summary>
        /// 报警消息
        /// </summary>
        public string EventMessage { get; set; }

        /// <summary>
        /// 初始触发时间
        /// </summary>
        public DateTime InitialTriggerTime { get; set; }

        /// <summary>
        /// 首次激活时间
        /// </summary>
        public DateTime? FirstActiveTime { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? AcknowledgedTime { get; set; }

        /// <summary>
        /// 确认人员
        /// </summary>
        public string AcknowledgedBy { get; set; }

        /// <summary>
        /// 解决时间
        /// </summary>
        public DateTime? ResolvedTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public string CurrentState { get; set; }

        /// <summary>
        /// 总持续时间
        /// </summary>
        public TimeSpan TotalDuration { get; set; }

        /// <summary>
        /// 是否当前激活
        /// </summary>
        public bool IsCurrentlyActive { get; set; }

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsAcknowledged { get; set; }

        /// <summary>
        /// 是否已解决
        /// </summary>
        public bool IsResolved { get; set; }

        /// <summary>
        /// 状态转换历史
        /// </summary>
        public List<AlarmStateTransition> StateTransitions { get; set; }

        /// <summary>
        /// 计算确认前时长 (TTA - Time To Acknowledge)
        /// </summary>
        public TimeSpan? TimeToAcknowledge
        {
            get
            {
                if (FirstActiveTime.HasValue && AcknowledgedTime.HasValue)
                {
                    return AcknowledgedTime.Value - FirstActiveTime.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// 计算解决前时长 (TTR - Time To Resolve)
        /// </summary>
        public TimeSpan? TimeToResolve
        {
            get
            {
                if (FirstActiveTime.HasValue && ResolvedTime.HasValue)
                {
                    return ResolvedTime.Value - FirstActiveTime.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// 是否为瞬时报警（直接从Active变为Inactive）
        /// </summary>
        public bool IsTransientAlarm
        {
            get
            {
                if (StateTransitions == null || StateTransitions.Count < 2)
                    return false;

                var orderedTransitions = StateTransitions.OrderBy(t => t.EventDateTime).ToList();

                // 检查是否存在从Active|Unacknowledged直接变为Inactive|Unacknowledged的情况
                for (int i = 0; i < orderedTransitions.Count - 1; i++)
                {
                    var current = orderedTransitions[i];
                    var next = orderedTransitions[i + 1];

                    if (current.EventState == "Active | Unacknowledged" &&
                        next.EventState == "Inactive | Unacknowledged")
                    {
                        return true;
                    }
                }

                return false;
            }
        }
    }

    /// <summary>
    /// 报警状态转换记录
    /// </summary>
    public class AlarmStateTransition
    {
        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventDateTime { get; set; }

        /// <summary>
        /// 事件状态
        /// </summary>
        public string EventState { get; set; }

        /// <summary>
        /// 事件ID
        /// </summary>
        public string EventId { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public decimal Severity { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 事件序列号
        /// </summary>
        public decimal EventSequence { get; set; }
    }

    /// <summary>
    /// 响应KPI结果
    /// </summary>
    public class ResponseKPIResult
    {
        /// <summary>
        /// TTA统计结果
        /// </summary>
        public TimeStatistics TTAStatistics { get; set; } = new TimeStatistics();

        /// <summary>
        /// TTR统计结果
        /// </summary>
        public TimeStatistics TTRStatistics { get; set; } = new TimeStatistics();

        /// <summary>
        /// 总生命周期数
        /// </summary>
        public int TotalLifecycles { get; set; }

        /// <summary>
        /// 已确认数量
        /// </summary>
        public int AcknowledgedCount { get; set; }

        /// <summary>
        /// 已解决数量
        /// </summary>
        public int ResolvedCount { get; set; }

        /// <summary>
        /// 当前激活数量
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// 确认率（%）
        /// </summary>
        public double AcknowledgmentRate { get; set; }

        /// <summary>
        /// 解决率（%）
        /// </summary>
        public double ResolutionRate { get; set; }

        /// <summary>
        /// 获取摘要信息
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"响应KPI摘要 - 总数: {TotalLifecycles}, 确认率: {AcknowledgmentRate:F1}%, 解决率: {ResolutionRate:F1}%, " +
                   $"平均TTA: {TTAStatistics.AverageValue.TotalMinutes:F1}分钟, 平均TTR: {TTRStatistics.AverageValue.TotalMinutes:F1}分钟";
        }
    }

    /// <summary>
    /// 时间统计结果
    /// </summary>
    public class TimeStatistics
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string MetricName { get; set; }

        /// <summary>
        /// 样本数量
        /// </summary>
        public int SampleCount { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public TimeSpan MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public TimeSpan MaxValue { get; set; }

        /// <summary>
        /// 平均值
        /// </summary>
        public TimeSpan AverageValue { get; set; }

        /// <summary>
        /// 中位数
        /// </summary>
        public TimeSpan MedianValue { get; set; }

        /// <summary>
        /// 标准差
        /// </summary>
        public TimeSpan StandardDeviation { get; set; }

        /// <summary>
        /// 95百分位数
        /// </summary>
        public TimeSpan Percentile95 { get; set; }

        /// <summary>
        /// 99百分位数
        /// </summary>
        public TimeSpan Percentile99 { get; set; }
    }

    /// <summary>
    /// 抖动报警检测结果
    /// </summary>
    public class FlutterAlarmResult
    {
        /// <summary>
        /// 报警源名称
        /// </summary>
        public string SourceName { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string Station { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Device { get; set; }

        /// <summary>
        /// 报警消息
        /// </summary>
        public string EventMessage { get; set; }

        /// <summary>
        /// 窗口开始时间
        /// </summary>
        public DateTime WindowStartTime { get; set; }

        /// <summary>
        /// 窗口结束时间
        /// </summary>
        public DateTime WindowEndTime { get; set; }

        /// <summary>
        /// 窗口大小
        /// </summary>
        public TimeSpan WindowSize { get; set; }

        /// <summary>
        /// 激活次数
        /// </summary>
        public int ActivationCount { get; set; }

        /// <summary>
        /// 抖动事件列表
        /// </summary>
        public List<FlutterEventInfo> FlutterEvents { get; set; } = new List<FlutterEventInfo>();

        /// <summary>
        /// 获取摘要信息
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"抖动报警 - {Station}.{Device}: {EventMessage}, " +
                   $"时间窗口: {WindowStartTime:HH:mm:ss}-{WindowEndTime:HH:mm:ss}, 激活次数: {ActivationCount}";
        }
    }

    /// <summary>
    /// 抖动事件信息
    /// </summary>
    public class FlutterEventInfo
    {
        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventDateTime { get; set; }

        /// <summary>
        /// 事件ID
        /// </summary>
        public string EventId { get; set; }

        /// <summary>
        /// 事件状态
        /// </summary>
        public string EventState { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public decimal Severity { get; set; }
    }

    /// <summary>
    /// 瞬时报警检测结果
    /// </summary>
    public class TransientAlarmResult
    {
        /// <summary>
        /// 生命周期ID
        /// </summary>
        public string LifecycleId { get; set; }

        /// <summary>
        /// 报警源名称
        /// </summary>
        public string SourceName { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string Station { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Device { get; set; }

        /// <summary>
        /// 报警消息
        /// </summary>
        public string EventMessage { get; set; }

        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivationTime { get; set; }

        /// <summary>
        /// 失活时间
        /// </summary>
        public DateTime DeactivationTime { get; set; }

        /// <summary>
        /// 持续时间
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 状态转换列表
        /// </summary>
        public List<TransientStateInfo> StateTransitions { get; set; } = new List<TransientStateInfo>();

        /// <summary>
        /// 获取摘要信息
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"瞬时报警 - {Station}.{Device}: {EventMessage}, " +
                   $"持续时间: {Duration.TotalMinutes:F2}分钟, 激活时间: {ActivationTime:HH:mm:ss}";
        }
    }

    /// <summary>
    /// 瞬时状态信息
    /// </summary>
    public class TransientStateInfo
    {
        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventDateTime { get; set; }

        /// <summary>
        /// 事件状态
        /// </summary>
        public string EventState { get; set; }

        /// <summary>
        /// 事件ID
        /// </summary>
        public string EventId { get; set; }
    }

    #endregion
}
// {{END_MODIFICATIONS}}
