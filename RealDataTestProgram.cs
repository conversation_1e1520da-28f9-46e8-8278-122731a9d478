// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Real-Data-Compatibility-Test"
//   Timestamp: "2024-12-19T12:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-Real-Data-Testing"
//   Quality_Check: "Testing program for UFUAAuditLogItem table compatibility."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using AlarmAnalysis.Models;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Services;
using AlarmAnalysis.Common;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// 针对UFUAAuditLogItem表的实际数据测试程序
    /// </summary>
    public class RealDataTestProgram
    {
        public static void Main(string[] args)
        {
            // 设置控制台编码为UTF-8以正确显示中文
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
            
            Console.WriteLine("=== UFUAAuditLogItem 数据兼容性测试 ===\n");
            
            try
            {
                // 测试1: 验证数据模型兼容性
                TestRealDataModel();
                
                // 测试2: 验证SourceName解析
                TestSourceNameParsing();
                
                // 测试3: 验证状态判断
                TestEventStateAnalysis();
                
                // 测试4: 验证分析功能
                TestAnalysisWithRealData();
                
                Console.WriteLine("\n=== 实际数据兼容性测试完成 ===");
                Console.WriteLine("程序已适配UFUAAuditLogItem表结构！✅");
                
                // 显示使用说明
                DisplayUsageInstructions();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 测试实际数据模型兼容性
        /// </summary>
        private static void TestRealDataModel()
        {
            Console.WriteLine("1. 测试 UFUAAuditLogItem 数据模型兼容性...");
            
            // 基于实际JSON数据创建测试对象
            var realAlarm = new AlarmEvent("PingShanStation/FT_2131/Source")
            {
                EventId = "54CA1E4E-BDB0-4820-9551-2719356D1F60",
                EventType = "i=10751",
                SourceNode = "ns=2;g=a6d33785-cf5b-4dcd-81aa-034016d855c2",
                EventDateTime = DateTime.Parse("2025-04-24 13:54:12.803"),
                EventDateTimeUtc = DateTime.Parse("2025-04-24 05:54:12.803"),
                EventMessage = "东部电厂FT_2131超声波流量计液体检测",
                EventDetails = "Tags.PingShanStation.FT_2131.IsLiquidDetected:PingShanStation/FT_2131/Source/Equal_1_alarm",
                EventState = "Active | Unacknowledged",
                Severity = 100.0m,
                EventDuration = 0.0,
                EventOccurence = 1.0m,
                EventSequence = 1.0m,
                RedundancySyncTime = DateTime.Parse("2025-04-24 05:54:12.807")
            };
            
            Console.WriteLine($"  EventId: {realAlarm.EventId}");
            Console.WriteLine($"  SourceName: {realAlarm.SourceName}");
            Console.WriteLine($"  Station: {realAlarm.Station}, Device: {realAlarm.Device}");
            Console.WriteLine($"  EventMessage: {realAlarm.EventMessage}");
            Console.WriteLine($"  EventState: {realAlarm.EventState}");
            Console.WriteLine($"  Severity: {realAlarm.Severity} (Priority: {realAlarm.Priority})");
            Console.WriteLine($"  Category: {realAlarm.Category}");
            Console.WriteLine($"  IsActive: {realAlarm.IsActive}");
            Console.WriteLine($"  IsUnacknowledged: {realAlarm.IsUnacknowledged}");
            Console.WriteLine($"  IsLongStandingAlarm: {realAlarm.IsLongStandingAlarm}");
            
            Console.WriteLine("  ✅ 数据模型兼容性测试通过\n");
        }
        
        /// <summary>
        /// 测试SourceName解析功能
        /// </summary>
        private static void TestSourceNameParsing()
        {
            Console.WriteLine("2. 测试 SourceName 解析功能...");
            
            var testCases = new[]
            {
                "PingShanStation/FT_2131/Source",
                "EastPlant/Pump_001/Status", 
                "WestStation/Tank-Level/Alarm",
                "Station1.Device1.Sensor",
                "ComplexStation/SubSystem/Device/Component"
            };
            
            foreach (var sourceName in testCases)
            {
                var alarm = new AlarmEvent(sourceName);
                Console.WriteLine($"  {sourceName} -> Station: {alarm.Station}, Device: {alarm.Device}");
            }
            
            Console.WriteLine("  ✅ SourceName解析测试通过\n");
        }
        
        /// <summary>
        /// 测试事件状态分析
        /// </summary>
        private static void TestEventStateAnalysis()
        {
            Console.WriteLine("3. 测试事件状态分析...");
            
            var stateTestCases = new[]
            {
                "Active | Unacknowledged",
                "Active | Acknowledged",
                "Inactive | Unacknowledged", 
                "Inactive | Acknowledged"
            };
            
            foreach (var state in stateTestCases)
            {
                var alarm = new AlarmEvent("Test/Device")
                {
                    EventState = state,
                    Severity = 100m
                };
                
                Console.WriteLine($"  状态: {state}");
                Console.WriteLine($"    IsActive: {alarm.IsActive}");
                Console.WriteLine($"    IsUnacknowledged: {alarm.IsUnacknowledged}");
                Console.WriteLine($"    IsLongStanding: {alarm.IsLongStandingAlarm}");
                Console.WriteLine($"    IsStale: {alarm.IsStaleAlarm}");
            }
            
            Console.WriteLine("  ✅ 事件状态分析测试通过\n");
        }
        
        /// <summary>
        /// 测试分析功能
        /// </summary>
        private static void TestAnalysisWithRealData()
        {
            Console.WriteLine("4. 测试分析功能...");
            
            // 创建基于实际数据格式的测试数据
            var realDataAlarms = CreateRealDataTestAlarms();
            Console.WriteLine($"  创建了 {realDataAlarms.Count} 条基于实际数据格式的测试报警");
            
            var analyzer = new BasicAlarmAnalyzer(5);
            
            // Top N分析
            var topMessages = analyzer.GetTopFrequentAlarmMessages(realDataAlarms);
            Console.WriteLine($"  Top 报警消息: {topMessages.Count} 项");
            foreach (var msg in topMessages)
            {
                Console.WriteLine($"    {msg.ItemName}: {msg.Count} 次");
            }
            
            var topStations = analyzer.GetTopAlarmingStations(realDataAlarms);
            Console.WriteLine($"  Top 报警站点: {topStations.Count} 项");
            foreach (var station in topStations)
            {
                Console.WriteLine($"    {station.ItemName}: {station.Count} 次报警");
            }
            
            // 报警率分析
            var rates = analyzer.CalculateAlarmRates(realDataAlarms);
            Console.WriteLine($"  总报警数: {rates.TotalAlarms}");
            Console.WriteLine($"  平均报警率: {rates.AlarmsPerHour:F2} 次/小时");
            
            // 状态分析
            var longStanding = analyzer.IdentifyLongStandingAlarms(realDataAlarms);
            var staleAlarms = analyzer.IdentifyStaleAlarms(realDataAlarms);
            Console.WriteLine($"  长期持续报警: {longStanding.Count} 项");
            Console.WriteLine($"  陈旧报警: {staleAlarms.Count} 项");
            
            Console.WriteLine("  ✅ 分析功能测试通过\n");
        }
        
        /// <summary>
        /// 创建基于实际数据格式的测试数据
        /// </summary>
        private static List<AlarmEvent> CreateRealDataTestAlarms()
        {
            var alarms = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-1);
            
            var stations = new[] { "PingShanStation", "EastPlant", "WestStation", "NorthFacility" };
            var devices = new[] { "FT_2131", "Pump_001", "Tank_Level", "Valve_A1", "Sensor_T1" };
            var messages = new[] 
            {
                "超声波流量计液体检测",
                "泵站压力异常报警",
                "储罐液位高位报警",
                "阀门开启状态异常",
                "温度传感器故障报警"
            };
            var states = new[]
            {
                "Active | Unacknowledged",
                "Active | Acknowledged",
                "Inactive | Unacknowledged", 
                "Inactive | Acknowledged"
            };
            
            // 生成50条测试数据
            for (int i = 0; i < 50; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var sourceName = $"{station}/{device}/Source";
                
                var alarm = new AlarmEvent(sourceName)
                {
                    EventId = Guid.NewGuid().ToString(),
                    EventType = "i=10751",
                    SourceNode = $"ns=2;g={Guid.NewGuid()}",
                    EventDateTime = baseTime.AddMinutes(random.Next(0, 1440)),
                    EventDateTimeUtc = baseTime.AddMinutes(random.Next(0, 1440)).ToUniversalTime(),
                    EventMessage = messages[random.Next(messages.Length)],
                    EventDetails = $"Tags.{station}.{device}.Alarm:Details",
                    EventState = states[random.Next(states.Length)],
                    Severity = random.Next(1, 4) * 50m, // 50, 100, 150
                    EventDuration = random.NextDouble() * 100,
                    EventOccurence = random.Next(1, 10),
                    EventSequence = i + 1,
                    RedundancySyncTime = baseTime.AddMinutes(random.Next(0, 1440))
                };
                
                alarms.Add(alarm);
            }
            
            // 按时间排序
            alarms.Sort((a, b) => a.EventDateTime.CompareTo(b.EventDateTime));
            
            return alarms;
        }
        
        /// <summary>
        /// 显示使用说明
        /// </summary>
        private static void DisplayUsageInstructions()
        {
            Console.WriteLine("\n" + "=".PadRight(60, '='));
            Console.WriteLine("UFUAAuditLogItem 表使用说明");
            Console.WriteLine("=".PadRight(60, '='));
            
            Console.WriteLine("\n📋 数据库配置:");
            Console.WriteLine("  数据库: DaPeng_IOServer");
            Console.WriteLine("  表名: UFUAAuditLogItem");
            Console.WriteLine("  连接字符串: Data Source=.;Initial Catalog=DaPeng_IOServer;Integrated Security=True");
            
            Console.WriteLine("\n📊 支持的分析功能:");
            Console.WriteLine("  ✅ Top N 最频繁报警消息");
            Console.WriteLine("  ✅ Top N 报警最多站点/设备");
            Console.WriteLine("  ✅ 报警率统计（平均/峰值）");
            Console.WriteLine("  ✅ 长期持续报警识别 (Active | Unacknowledged)");
            Console.WriteLine("  ✅ 陈旧报警识别 (Inactive | Unacknowledged)");
            
            Console.WriteLine("\n🔧 字段映射:");
            Console.WriteLine("  EventId -> 事件唯一标识");
            Console.WriteLine("  SourceName -> 报警源 (Station/Device/Source 格式)");
            Console.WriteLine("  EventMessage -> 报警消息（中文）");
            Console.WriteLine("  EventState -> 报警状态 (Active|Inactive | Acknowledged|Unacknowledged)");
            Console.WriteLine("  Severity -> 严重程度（数值，100=高优先级）");
            Console.WriteLine("  EventType -> 事件类型 (i=10751 等OPC UA格式)");
            
            Console.WriteLine("\n💡 使用示例:");
            Console.WriteLine("  using (var service = new AlarmAnalysisService())");
            Console.WriteLine("  {");
            Console.WriteLine("      var alarms = service.LoadAlarmData(startTime, endTime, \"UFUAAuditLogItem\");");
            Console.WriteLine("      var result = service.PerformCompleteAnalysis(alarms);");
            Console.WriteLine("      Console.WriteLine(result.GetSummary());");
            Console.WriteLine("  }");
            
            Console.WriteLine("\n" + "=".PadRight(60, '='));
        }
    }
}
// {{END_MODIFICATIONS}}
