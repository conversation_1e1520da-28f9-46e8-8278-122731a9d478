# 报警历史数据分析工具

## 项目概述

这是一个基于 .NET Framework 4.8 和 DevExpress WinForms 的桌面应用程序，用于深度分析从 SQL Server 数据库导出的报警历史数据，提供可操作的运营洞察。

## 已实现功能

### Phase 1: 项目基础架构与数据接入层 ✅

#### 1. 数据模型 (`Models/AlarmEvent.cs`)
- **AlarmEvent** 类：完整映射报警历史表字段
- **自动解析功能**：从 `SourceName` 自动提取 `Station` 和 `Device` 信息
- **支持多种命名模式**：
  - `Station.Device`
  - `Station_Device`
  - `Station-Device`
  - 驼峰命名自动分割
- **状态判断方法**：提供便捷的报警状态检查方法

#### 2. 高性能数据访问 (`DataAccess/AlarmDataReader.cs`)
- **流式读取**：使用 ADO.NET 和 SqlDataReader 实现低内存占用
- **时间排序**：自动按 `EventDateTime ASC` 排序
- **批处理支持**：支持大数据集的分批处理
- **异步操作**：提供完整的异步读取支持
- **连接管理**：自动资源释放和连接池优化

#### 3. 配置管理 (`Common/ConfigurationHelper.cs`)
- **类型安全配置读取**：支持各种数据类型的配置项
- **默认值处理**：配置缺失时的智能默认值
- **配置验证**：启动时自动验证所有必需配置
- **分析参数集中管理**：Top N 数量、时间窗口等参数

#### 4. 异常处理 (`Common/AlarmAnalysisException.cs`)
- **分层异常体系**：
  - `DataAccessException`：数据访问相关异常
  - `AnalysisException`：数据分析相关异常
  - `ConfigurationException`：配置相关异常
  - `ValidationException`：数据验证相关异常
- **详细错误信息**：包含上下文信息便于调试

### Phase 2: 核心分析引擎 - 基础频率与分布指标 ✅

#### 1. Top N 频率分析 (`Analysis/BasicAlarmAnalyzer.cs`)
- **最频繁报警消息**：统计发生次数最多的报警类型
- **报警最多设备**：识别问题设备并统计报警类型数
- **报警最多站点**：识别问题区域并统计涉及设备数
- **详细统计信息**：
  - 发生次数和百分比
  - 首次和最后发生时间
  - 平均发生间隔
  - 相关统计（设备数、报警类型数）

#### 2. 报警率计算
- **总体报警率**：按小时/天计算平均报警率
- **峰值报警率**：滑动窗口（默认10分钟）内的最大报警数
- **时间分析**：
  - 分析时间跨度统计
  - 平均报警间隔计算
  - 峰值时间段识别

#### 3. 持续与陈旧报警识别
- **长期持续报警**：识别 `Active | Unconfirmed` 状态的报警
  - 持续时长计算
  - 发生频次统计
  - 优先级和类别信息
- **陈旧报警**：识别 `Inactive | Unconfirmed` 状态的报警
  - 陈旧时长计算
  - 状态转换次数统计
  - 历史跟踪信息

#### 4. 服务层封装 (`Services/AlarmAnalysisService.cs`)
- **统一接口**：提供一站式分析服务
- **完整分析**：一键执行所有 Phase 2 分析功能
- **资源管理**：自动处理数据库连接和内存释放
- **结果整合**：结构化的分析结果输出

## 技术特性

### 性能优化
- **流式处理**：支持大数据集的流式读取和分析
- **批处理**：可配置的批处理大小，平衡内存和性能
- **异步支持**：全面的异步操作支持，避免UI阻塞
- **LINQ优化**：高效的数据查询和聚合操作

### 健壮性
- **空数据处理**：所有分析功能都能正确处理空数据集
- **参数验证**：完整的输入参数验证
- **异常恢复**：分层异常处理和错误恢复机制
- **资源管理**：IDisposable 模式确保资源正确释放

### 可扩展性
- **模块化设计**：清晰的分层架构便于扩展
- **配置驱动**：关键参数通过配置文件管理
- **接口抽象**：为后续功能扩展预留接口
- **插件架构**：分析器可独立扩展新功能

## 配置说明

### 数据库连接 (`App.config`)
```xml
<connectionStrings>
  <add name="AlarmDatabase" 
       connectionString="Data Source=localhost;Initial Catalog=AlarmHistory;Integrated Security=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 分析参数
```xml
<appSettings>
  <add key="TopNCount" value="10" />                    <!-- Top N 统计数量 -->
  <add key="FlutterTimeWindowSeconds" value="60" />     <!-- 抖动检测窗口(秒) -->
  <add key="FlutterThreshold" value="3" />              <!-- 抖动检测阈值(次数) -->
  <add key="FloodTimeWindowMinutes" value="10" />       <!-- 报警风暴窗口(分钟) -->
  <add key="FloodThreshold" value="10" />               <!-- 报警风暴阈值(次数) -->
  <add key="DatabaseTimeoutSeconds" value="30" />       <!-- 数据库超时(秒) -->
  <add key="BatchSize" value="1000" />                  <!-- 批处理大小 -->
</appSettings>
```

## 使用示例

### 基本使用
```csharp
using (var analysisService = new AlarmAnalysisService())
{
    // 测试连接
    if (!analysisService.TestDatabaseConnection())
    {
        Console.WriteLine("数据库连接失败");
        return;
    }
    
    // 加载数据
    var startTime = DateTime.Now.AddDays(-7);
    var endTime = DateTime.Now;
    var alarmEvents = analysisService.LoadAlarmData(startTime, endTime);
    
    // 执行完整分析
    var result = analysisService.PerformCompleteAnalysis(alarmEvents);
    
    // 显示结果
    Console.WriteLine(result.GetSummary());
}
```

### 异步处理
```csharp
using (var analysisService = new AlarmAnalysisService())
{
    var alarmEvents = await analysisService.LoadAlarmDataAsync(startTime, endTime);
    var result = analysisService.PerformCompleteAnalysis(alarmEvents);
}
```

### 流式处理大数据
```csharp
var allEvents = new List<AlarmEvent>();
analysisService.LoadAlarmDataStream(
    startTime, endTime,
    batch => {
        allEvents.AddRange(batch);
        Console.WriteLine($"处理了 {batch.Count} 条记录");
    },
    batchSize: 5000
);
```

## 项目结构

```
AlarmAnalysis/
├── Models/                     # 数据模型
│   └── AlarmEvent.cs          # 报警事件模型
├── DataAccess/                # 数据访问层
│   └── AlarmDataReader.cs     # 高性能数据读取器
├── Analysis/                  # 分析引擎
│   └── BasicAlarmAnalyzer.cs  # 基础分析器
├── Services/                  # 服务层
│   └── AlarmAnalysisService.cs # 分析服务
├── Common/                    # 公共组件
│   ├── ConfigurationHelper.cs # 配置管理
│   └── AlarmAnalysisException.cs # 异常定义
├── Examples/                  # 使用示例
│   └── UsageExample.cs        # 完整使用示例
└── App.config                 # 应用配置
```

## 下一步计划

### Phase 3: 核心分析引擎 - 时间与行为分析
- 报警生命周期重构
- 响应KPI计算 (TTA/TTR)
- 抖动与瞬时报警检测

### Phase 4: 高级关联与序列分析
- 报警风暴分析
- 序列模式挖掘
- 关联规则发现

### Phase 5: 用户界面、可视化与报告
- DevExpress 界面开发
- ScottPlot 图表集成
- 报告导出功能

## 技术要求

- .NET Framework 4.8
- DevExpress WinForms v24.2
- SQL Server (任何版本)
- Visual Studio 2019 或更高版本

## 注意事项

1. **数据库表结构**：确保报警历史表包含所需字段
2. **权限配置**：确保应用程序有足够的数据库读取权限
3. **内存管理**：大数据集分析时建议使用流式处理
4. **配置验证**：首次运行时会自动验证配置完整性

## 许可证

此项目仅供学习和开发使用。
