﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;

namespace AlarmAnalysis
{
    public partial class Form1 : DevExpress.XtraEditors.XtraForm
    {
        public Form1()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Phase 3测试按钮点击事件
        /// </summary>
        private void btnTestPhase3_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输出控件
                memoEdit1.Text = "";

                // 显示开始信息
                AppendOutput("=== Phase 3 高级报警分析测试 ===\n");
                Application.DoEvents();

                // 生成测试数据
                var testData = GenerateTestData();
                AppendOutput($"生成了 {testData.Count} 条测试记录\n");
                Application.DoEvents();

                // 测试1: 生命周期重构
                TestLifecycleReconstruction(testData);

                // 测试2: KPI计算
                TestKPICalculation(testData);

                // 测试3: 抖动检测
                TestFlutterDetection(testData);

                // 测试4: 瞬时报警检测
                TestTransientDetection(testData);

                // 测试5: 性能测试
                TestPerformance();

                AppendOutput("\n=== 所有测试成功通过！ ===");
                AppendOutput("Phase 3 功能实现正常工作。");
            }
            catch (Exception ex)
            {
                AppendOutput($"❌ 测试失败: {ex.Message}");
                AppendOutput($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 向输出控件添加文本
        /// </summary>
        private void AppendOutput(string text)
        {
            memoEdit1.Text += text + "\r\n";
            memoEdit1.SelectionStart = memoEdit1.Text.Length;
            memoEdit1.ScrollToCaret();
            Application.DoEvents();
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private List<AlarmEvent> GenerateTestData()
        {
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-12);

            // 生成正常报警序列
            for (int i = 0; i < 15; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 20);

                // 激活事件
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 3}/Source",
                    EventMessage = $"Test Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i * 3
                });

                // 确认事件 (70%的概率)
                if (random.Next(10) < 7)
                {
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm {i % 2}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(random.Next(2, 8)),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 1
                    });

                    // 解决事件 (80%的概率)
                    if (random.Next(10) < 8)
                    {
                        testData.Add(new AlarmEvent
                        {
                            EventId = Guid.NewGuid().ToString(),
                            SourceName = $"TestStation/Device_{i % 3}/Source",
                            EventMessage = $"Test Alarm {i % 2}",
                            EventState = "Inactive | Acknowledged",
                            EventDateTime = alarmTime.AddMinutes(random.Next(10, 25)),
                            Severity = 100,
                            UserName = "TestUser",
                            EventSequence = i * 3 + 2
                        });
                    }
                }
            }

            // 生成抖动报警 (在60秒内激活4次)
            var flutterTime = baseTime.AddHours(6);
            for (int i = 0; i < 4; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "Flutter Alarm",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 15), // 每15秒激活一次
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }

            // 生成瞬时报警 (直接从Active变为Inactive)
            var transientTime = baseTime.AddHours(8);
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });

            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(1.5), // 1.5分钟后直接变为Inactive
                Severity = 100,
                EventSequence = 2001
            });

            return testData.OrderBy(e => e.EventDateTime).ToList();
        }

        /// <summary>
        /// 测试生命周期重构功能
        /// </summary>
        private void TestLifecycleReconstruction(List<AlarmEvent> testData)
        {
            AppendOutput("🔄 测试 1: 生命周期重构");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3)) // 使用参数构造函数避免配置依赖
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);

                    AppendOutput($"  ✓ 生成了 {lifecycles.Count} 个生命周期");

                    var sampleLifecycle = lifecycles.Values.FirstOrDefault();
                    if (sampleLifecycle != null)
                    {
                        AppendOutput($"  示例: {sampleLifecycle.Station}.{sampleLifecycle.Device}");
                        AppendOutput($"    确认时长: {sampleLifecycle.TimeToAcknowledge?.TotalMinutes:F1} 分钟");
                        AppendOutput($"    解决时长: {sampleLifecycle.TimeToResolve?.TotalMinutes:F1} 分钟");
                        AppendOutput($"    瞬时报警: {sampleLifecycle.IsTransientAlarm}");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 生命周期重构测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试KPI计算功能
        /// </summary>
        private void TestKPICalculation(List<AlarmEvent> testData)
        {
            AppendOutput("\n📊 测试 2: KPI 计算");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);

                    AppendOutput($"  ✓ KPI 计算完成");
                    AppendOutput($"    总生命周期数: {kpis.TotalLifecycles}");
                    AppendOutput($"    确认率: {kpis.AcknowledgmentRate:F1}%");
                    AppendOutput($"    解决率: {kpis.ResolutionRate:F1}%");
                    AppendOutput($"    平均确认时长: {kpis.TTAStatistics.AverageValue.TotalMinutes:F1} 分钟");
                    AppendOutput($"    平均解决时长: {kpis.TTRStatistics.AverageValue.TotalMinutes:F1} 分钟");
                    AppendOutput($"    确认时长中位数: {kpis.TTAStatistics.MedianValue.TotalMinutes:F1} 分钟");
                    AppendOutput($"    解决时长中位数: {kpis.TTRStatistics.MedianValue.TotalMinutes:F1} 分钟");
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ KPI 计算测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试抖动检测功能
        /// </summary>
        private void TestFlutterDetection(List<AlarmEvent> testData)
        {
            AppendOutput("\n⚡ 测试 3: 抖动检测");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var flutters = analyzer.DetectFlutterAlarms(testData);

                    AppendOutput($"  ✓ 检测到 {flutters.Count} 个抖动报警");

                    foreach (var flutter in flutters.Take(3))
                    {
                        AppendOutput($"    {flutter.Station}.{flutter.Device}: 在 {flutter.WindowSize.TotalSeconds} 秒内激活 {flutter.ActivationCount} 次");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 抖动检测测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试瞬时报警检测功能
        /// </summary>
        private void TestTransientDetection(List<AlarmEvent> testData)
        {
            AppendOutput("\n⚡ 测试 4: 瞬时报警检测");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);

                    AppendOutput($"  ✓ 检测到 {transients.Count} 个瞬时报警");

                    foreach (var transient in transients.Take(3))
                    {
                        AppendOutput($"    {transient.Station}.{transient.Device}: 持续时间 {transient.Duration.TotalMinutes:F1} 分钟");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 瞬时报警检测测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        private void TestPerformance()
        {
            AppendOutput("\n🚀 测试 5: 性能测试");

            try
            {
                var largeDataset = GenerateLargeDataset(2000);
                AppendOutput($"  生成测试数据: {largeDataset.Count:N0} 条记录");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(largeDataset);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);
                    var flutters = analyzer.DetectFlutterAlarms(largeDataset);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);

                    stopwatch.Stop();

                    AppendOutput($"  ✓ 大数据集分析完成");
                    AppendOutput($"    处理时间: {stopwatch.ElapsedMilliseconds:N0} 毫秒");
                    AppendOutput($"    处理速度: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} 条记录/秒");
                    AppendOutput($"    生成了 {lifecycles.Count:N0} 个生命周期");
                    AppendOutput($"    发现 {flutters.Count:N0} 个抖动报警");
                    AppendOutput($"    发现 {transients.Count:N0} 个瞬时报警");
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 性能测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成大数据集用于性能测试
        /// </summary>
        private List<AlarmEvent> GenerateLargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-2);

            var stations = new[] { "Station1", "Station2", "Station3", "Station4" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm", "Level Alarm" };

            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];

                var alarmTime = baseTime.AddMinutes(i * 0.5 + random.Next(0, 30));

                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 8 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });
            }

            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
