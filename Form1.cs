﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;

namespace AlarmAnalysis
{
    public partial class Form1 : DevExpress.XtraEditors.XtraForm
    {
        public Form1()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Phase 3测试按钮点击事件
        /// </summary>
        private void btnTestPhase3_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输出控件
                memoEdit1.Text = "";

                // 显示开始信息
                AppendOutput("=== Phase 3 Advanced Alarm Analysis Test ===\n");
                Application.DoEvents();

                // 生成测试数据
                var testData = GenerateTestData();
                AppendOutput($"Generated {testData.Count} test records\n");
                Application.DoEvents();

                // 测试1: 生命周期重构
                TestLifecycleReconstruction(testData);

                // 测试2: KPI计算
                TestKPICalculation(testData);

                // 测试3: 抖动检测
                TestFlutterDetection(testData);

                // 测试4: 瞬时报警检测
                TestTransientDetection(testData);

                // 测试5: 性能测试
                TestPerformance();

                AppendOutput("\n=== All Tests Passed Successfully! ===");
                AppendOutput("Phase 3 implementation is working correctly.");
            }
            catch (Exception ex)
            {
                AppendOutput($"❌ Test failed: {ex.Message}");
                AppendOutput($"Details: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 向输出控件添加文本
        /// </summary>
        private void AppendOutput(string text)
        {
            memoEdit1.Text += text + "\r\n";
            memoEdit1.SelectionStart = memoEdit1.Text.Length;
            memoEdit1.ScrollToCaret();
            Application.DoEvents();
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private List<AlarmEvent> GenerateTestData()
        {
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-12);

            // 生成正常报警序列
            for (int i = 0; i < 15; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 20);

                // 激活事件
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 3}/Source",
                    EventMessage = $"Test Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i * 3
                });

                // 确认事件 (70%的概率)
                if (random.Next(10) < 7)
                {
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm {i % 2}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(random.Next(2, 8)),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 1
                    });

                    // 解决事件 (80%的概率)
                    if (random.Next(10) < 8)
                    {
                        testData.Add(new AlarmEvent
                        {
                            EventId = Guid.NewGuid().ToString(),
                            SourceName = $"TestStation/Device_{i % 3}/Source",
                            EventMessage = $"Test Alarm {i % 2}",
                            EventState = "Inactive | Acknowledged",
                            EventDateTime = alarmTime.AddMinutes(random.Next(10, 25)),
                            Severity = 100,
                            UserName = "TestUser",
                            EventSequence = i * 3 + 2
                        });
                    }
                }
            }

            // 生成抖动报警 (在60秒内激活4次)
            var flutterTime = baseTime.AddHours(6);
            for (int i = 0; i < 4; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "Flutter Alarm",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 15), // 每15秒激活一次
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }

            // 生成瞬时报警 (直接从Active变为Inactive)
            var transientTime = baseTime.AddHours(8);
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });

            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(1.5), // 1.5分钟后直接变为Inactive
                Severity = 100,
                EventSequence = 2001
            });

            return testData.OrderBy(e => e.EventDateTime).ToList();
        }

        /// <summary>
        /// 测试生命周期重构功能
        /// </summary>
        private void TestLifecycleReconstruction(List<AlarmEvent> testData)
        {
            AppendOutput("🔄 Test 1: Lifecycle Reconstruction");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3)) // 使用参数构造函数避免配置依赖
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);

                    AppendOutput($"  ✓ Generated {lifecycles.Count} lifecycles");

                    var sampleLifecycle = lifecycles.Values.FirstOrDefault();
                    if (sampleLifecycle != null)
                    {
                        AppendOutput($"  Sample: {sampleLifecycle.Station}.{sampleLifecycle.Device}");
                        AppendOutput($"    TTA: {sampleLifecycle.TimeToAcknowledge?.TotalMinutes:F1} min");
                        AppendOutput($"    TTR: {sampleLifecycle.TimeToResolve?.TotalMinutes:F1} min");
                        AppendOutput($"    Transient: {sampleLifecycle.IsTransientAlarm}");
                    }
                }
                AppendOutput("  ✓ Passed");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ Lifecycle reconstruction test failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试KPI计算功能
        /// </summary>
        private void TestKPICalculation(List<AlarmEvent> testData)
        {
            AppendOutput("\n📊 Test 2: KPI Calculation");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);

                    AppendOutput($"  ✓ KPI calculation completed");
                    AppendOutput($"    Total lifecycles: {kpis.TotalLifecycles}");
                    AppendOutput($"    Acknowledgment rate: {kpis.AcknowledgmentRate:F1}%");
                    AppendOutput($"    Resolution rate: {kpis.ResolutionRate:F1}%");
                    AppendOutput($"    Avg TTA: {kpis.TTAStatistics.AverageValue.TotalMinutes:F1} min");
                    AppendOutput($"    Avg TTR: {kpis.TTRStatistics.AverageValue.TotalMinutes:F1} min");
                    AppendOutput($"    TTA Median: {kpis.TTAStatistics.MedianValue.TotalMinutes:F1} min");
                    AppendOutput($"    TTR Median: {kpis.TTRStatistics.MedianValue.TotalMinutes:F1} min");
                }
                AppendOutput("  ✓ Passed");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ KPI calculation test failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试抖动检测功能
        /// </summary>
        private void TestFlutterDetection(List<AlarmEvent> testData)
        {
            AppendOutput("\n⚡ Test 3: Flutter Detection");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var flutters = analyzer.DetectFlutterAlarms(testData);

                    AppendOutput($"  ✓ Detected {flutters.Count} flutter alarms");

                    foreach (var flutter in flutters.Take(3))
                    {
                        AppendOutput($"    {flutter.Station}.{flutter.Device}: {flutter.ActivationCount} activations in {flutter.WindowSize.TotalSeconds}s");
                    }
                }
                AppendOutput("  ✓ Passed");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ Flutter detection test failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试瞬时报警检测功能
        /// </summary>
        private void TestTransientDetection(List<AlarmEvent> testData)
        {
            AppendOutput("\n⚡ Test 4: Transient Detection");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);

                    AppendOutput($"  ✓ Detected {transients.Count} transient alarms");

                    foreach (var transient in transients.Take(3))
                    {
                        AppendOutput($"    {transient.Station}.{transient.Device}: {transient.Duration.TotalMinutes:F1} min duration");
                    }
                }
                AppendOutput("  ✓ Passed");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ Transient detection test failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        private void TestPerformance()
        {
            AppendOutput("\n🚀 Test 5: Performance Test");

            try
            {
                var largeDataset = GenerateLargeDataset(2000);
                AppendOutput($"  Generated test data: {largeDataset.Count:N0} records");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(largeDataset);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);
                    var flutters = analyzer.DetectFlutterAlarms(largeDataset);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);

                    stopwatch.Stop();

                    AppendOutput($"  ✓ Large dataset analysis completed");
                    AppendOutput($"    Processing time: {stopwatch.ElapsedMilliseconds:N0} ms");
                    AppendOutput($"    Processing speed: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} records/sec");
                    AppendOutput($"    Generated {lifecycles.Count:N0} lifecycles");
                    AppendOutput($"    Found {flutters.Count:N0} flutter alarms");
                    AppendOutput($"    Found {transients.Count:N0} transient alarms");
                }
                AppendOutput("  ✓ Passed");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ Performance test failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成大数据集用于性能测试
        /// </summary>
        private List<AlarmEvent> GenerateLargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-2);

            var stations = new[] { "Station1", "Station2", "Station3", "Station4" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm", "Level Alarm" };

            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];

                var alarmTime = baseTime.AddMinutes(i * 0.5 + random.Next(0, 30));

                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 8 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });
            }

            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
