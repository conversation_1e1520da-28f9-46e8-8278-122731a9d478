// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase1-2-Usage-Example"
//   Timestamp: "2024-12-19T11:45:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-Documentation, Aether-Engineering-Example-Code"
//   Quality_Check: "Comprehensive usage examples demonstrating all Phase 1 and Phase 2 features."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlarmAnalysis.Common;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;

namespace AlarmAnalysis.Examples
{
    /// <summary>
    /// Phase 1 和 Phase 2 功能使用示例
    /// </summary>
    public static class UsageExample
    {
        /// <summary>
        /// 基本使用示例
        /// </summary>
        public static void BasicUsageExample()
        {
            Console.WriteLine("=== 报警分析系统 - 基本使用示例 ===\n");
            
            try
            {
                // 1. 验证配置
                Console.WriteLine("1. 验证配置...");
                var configValidation = ConfigurationHelper.ValidateConfiguration();
                Console.WriteLine(configValidation.GetSummary());
                
                if (!configValidation.IsValid)
                {
                    Console.WriteLine("配置验证失败，请检查App.config文件。");
                    return;
                }
                
                // 2. 创建服务实例
                Console.WriteLine("2. 创建分析服务...");
                using (var analysisService = new AlarmAnalysisService())
                {
                    // 3. 测试数据库连接
                    Console.WriteLine("3. 测试数据库连接...");
                    if (!analysisService.TestDatabaseConnection())
                    {
                        Console.WriteLine("数据库连接失败，请检查连接字符串。");
                        return;
                    }
                    Console.WriteLine("数据库连接成功！");
                    
                    // 4. 设置分析时间范围（最近7天）
                    var endTime = DateTime.Now;
                    var startTime = endTime.AddDays(-7);
                    Console.WriteLine($"4. 分析时间范围: {startTime:yyyy-MM-dd HH:mm:ss} - {endTime:yyyy-MM-dd HH:mm:ss}");
                    
                    // 5. 获取数据总数
                    var totalRecords = analysisService.GetRecordCount("AlarmHistory", startTime, endTime);
                    Console.WriteLine($"5. 数据总数: {totalRecords:N0} 条记录");
                    
                    if (totalRecords == 0)
                    {
                        Console.WriteLine("没有找到数据，请检查时间范围和表名。");
                        return;
                    }
                    
                    // 6. 加载数据
                    Console.WriteLine("6. 加载报警数据...");
                    var alarmEvents = analysisService.LoadAlarmData(startTime, endTime);
                    Console.WriteLine($"   成功加载 {alarmEvents.Count:N0} 条报警记录");
                    
                    // 7. 执行完整分析
                    Console.WriteLine("7. 执行分析...");
                    var analysisResult = analysisService.PerformCompleteAnalysis(alarmEvents);
                    
                    // 8. 显示分析结果
                    DisplayAnalysisResults(analysisResult);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"执行过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
            }
        }
        
        /// <summary>
        /// 异步使用示例
        /// </summary>
        public static async Task AsyncUsageExample()
        {
            Console.WriteLine("=== 报警分析系统 - 异步使用示例 ===\n");
            
            try
            {
                using (var analysisService = new AlarmAnalysisService())
                {
                    // 异步测试连接
                    Console.WriteLine("1. 异步测试数据库连接...");
                    var isConnected = await analysisService.TestDatabaseConnectionAsync();
                    if (!isConnected)
                    {
                        Console.WriteLine("数据库连接失败");
                        return;
                    }
                    Console.WriteLine("数据库连接成功！");
                    
                    // 异步加载数据
                    var endTime = DateTime.Now;
                    var startTime = endTime.AddHours(-24); // 最近24小时
                    
                    Console.WriteLine("2. 异步加载数据...");
                    var alarmEvents = await analysisService.LoadAlarmDataAsync(startTime, endTime);
                    Console.WriteLine($"   异步加载完成，共 {alarmEvents.Count:N0} 条记录");
                    
                    // 执行分析
                    Console.WriteLine("3. 执行分析...");
                    var analysisResult = analysisService.PerformCompleteAnalysis(alarmEvents);
                    
                    // 显示简要结果
                    Console.WriteLine("\n=== 分析摘要 ===");
                    Console.WriteLine(analysisResult.GetSummary());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步执行过程中发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 流式数据处理示例
        /// </summary>
        public static void StreamProcessingExample()
        {
            Console.WriteLine("=== 报警分析系统 - 流式处理示例 ===\n");
            
            try
            {
                using (var analysisService = new AlarmAnalysisService())
                {
                    var endTime = DateTime.Now;
                    var startTime = endTime.AddDays(-30); // 最近30天
                    
                    var allEvents = new List<AlarmEvent>();
                    var batchCount = 0;
                    
                    Console.WriteLine("开始流式处理数据...");
                    
                    analysisService.LoadAlarmDataStream(
                        startTime, 
                        endTime,
                        batch =>
                        {
                            batchCount++;
                            allEvents.AddRange(batch);
                            Console.WriteLine($"处理批次 {batchCount}: {batch.Count:N0} 条记录，累计: {allEvents.Count:N0} 条");
                        },
                        batchSize: 5000
                    );
                    
                    Console.WriteLine($"\n流式处理完成，总计: {allEvents.Count:N0} 条记录");
                    
                    // 对累积的数据进行分析
                    if (allEvents.Any())
                    {
                        Console.WriteLine("执行分析...");
                        var result = analysisService.PerformCompleteAnalysis(allEvents);
                        Console.WriteLine(result.GetSummary());
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"流式处理过程中发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 单独功能测试示例
        /// </summary>
        public static void IndividualFeaturesExample()
        {
            Console.WriteLine("=== 报警分析系统 - 单独功能测试示例 ===\n");
            
            try
            {
                using (var analysisService = new AlarmAnalysisService())
                {
                    var endTime = DateTime.Now;
                    var startTime = endTime.AddDays(-1); // 最近1天
                    
                    var alarmEvents = analysisService.LoadAlarmData(startTime, endTime);
                    if (!alarmEvents.Any())
                    {
                        Console.WriteLine("没有数据可供分析");
                        return;
                    }
                    
                    Console.WriteLine($"加载了 {alarmEvents.Count:N0} 条报警记录\n");
                    
                    // 1. Top N 报警消息
                    Console.WriteLine("=== Top 5 最频繁报警消息 ===");
                    var topMessages = analysisService.GetTopFrequentAlarms(alarmEvents, 5);
                    foreach (var msg in topMessages)
                    {
                        Console.WriteLine($"{msg.ItemName}: {msg.Count:N0} 次 ({msg.Percentage:F1}%)");
                    }
                    
                    // 2. Top N 设备
                    Console.WriteLine("\n=== Top 5 报警最多设备 ===");
                    var topDevices = analysisService.GetTopAlarmingDevices(alarmEvents, 5);
                    foreach (var device in topDevices)
                    {
                        Console.WriteLine($"{device.ItemName}: {device.Count:N0} 次报警，{device.UniqueAlarmTypes} 种类型");
                    }
                    
                    // 3. Top N 站点
                    Console.WriteLine("\n=== Top 5 报警最多站点 ===");
                    var topStations = analysisService.GetTopAlarmingStations(alarmEvents, 5);
                    foreach (var station in topStations)
                    {
                        Console.WriteLine($"{station.ItemName}: {station.Count:N0} 次报警，{station.UniqueDevices} 个设备");
                    }
                    
                    // 4. 报警率统计
                    Console.WriteLine("\n=== 报警率统计 ===");
                    var rates = analysisService.CalculateAlarmRates(alarmEvents);
                    Console.WriteLine($"平均报警率: {rates.AlarmsPerHour:F2} 次/小时");
                    Console.WriteLine($"峰值报警率: {rates.PeakAlarmRate.MaxAlarmsInWindow} 次/{rates.PeakAlarmRate.WindowSize.TotalMinutes:F0}分钟");
                    Console.WriteLine($"峰值时间: {rates.PeakAlarmRate.PeakStartTime:HH:mm:ss} - {rates.PeakAlarmRate.PeakEndTime:HH:mm:ss}");
                    
                    // 5. 长期持续报警
                    Console.WriteLine("\n=== 长期持续报警 ===");
                    var longStanding = analysisService.IdentifyLongStandingAlarms(alarmEvents);
                    Console.WriteLine($"发现 {longStanding.Count} 个长期持续报警");
                    foreach (var alarm in longStanding.Take(3))
                    {
                        Console.WriteLine($"  {alarm.Station}.{alarm.Device}: {alarm.EventMessage} (持续 {alarm.Duration.TotalHours:F1} 小时)");
                    }
                    
                    // 6. 陈旧报警
                    Console.WriteLine("\n=== 陈旧报警 ===");
                    var staleAlarms = analysisService.IdentifyStaleAlarms(alarmEvents);
                    Console.WriteLine($"发现 {staleAlarms.Count} 个陈旧报警");
                    foreach (var alarm in staleAlarms.Take(3))
                    {
                        Console.WriteLine($"  {alarm.Station}.{alarm.Device}: {alarm.EventMessage} (陈旧 {alarm.StaleDuration.TotalHours:F1} 小时)");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"单独功能测试过程中发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 显示完整分析结果
        /// </summary>
        /// <param name="result">分析结果</param>
        private static void DisplayAnalysisResults(CompleteAnalysisResult result)
        {
            Console.WriteLine("\n" + "=".PadRight(50, '='));
            Console.WriteLine("完整分析结果");
            Console.WriteLine("=".PadRight(50, '='));
            
            Console.WriteLine(result.GetSummary());
            
            // Top 报警消息
            if (result.TopAlarmMessages?.Any() == true)
            {
                Console.WriteLine("\n--- Top 报警消息 ---");
                foreach (var msg in result.TopAlarmMessages.Take(5))
                {
                    Console.WriteLine($"  {msg.ItemName}: {msg.Count:N0} 次 ({msg.Percentage:F1}%)");
                }
            }
            
            // Top 设备
            if (result.TopAlarmingDevices?.Any() == true)
            {
                Console.WriteLine("\n--- Top 报警设备 ---");
                foreach (var device in result.TopAlarmingDevices.Take(5))
                {
                    Console.WriteLine($"  {device.ItemName}: {device.Count:N0} 次，{device.UniqueAlarmTypes} 种类型");
                }
            }
            
            // Top 站点
            if (result.TopAlarmingStations?.Any() == true)
            {
                Console.WriteLine("\n--- Top 报警站点 ---");
                foreach (var station in result.TopAlarmingStations.Take(5))
                {
                    Console.WriteLine($"  {station.ItemName}: {station.Count:N0} 次，{station.UniqueDevices} 个设备");
                }
            }
            
            // 长期持续报警摘要
            if (result.LongStandingAlarms?.Any() == true)
            {
                Console.WriteLine($"\n--- 长期持续报警 (共{result.LongStandingAlarms.Count}个) ---");
                var topLongStanding = result.LongStandingAlarms
                    .OrderByDescending(a => a.Duration)
                    .Take(3);
                    
                foreach (var alarm in topLongStanding)
                {
                    Console.WriteLine($"  {alarm.Station}.{alarm.Device}: 持续 {alarm.Duration.TotalDays:F1} 天");
                }
            }
            
            // 陈旧报警摘要
            if (result.StaleAlarms?.Any() == true)
            {
                Console.WriteLine($"\n--- 陈旧报警 (共{result.StaleAlarms.Count}个) ---");
                var topStale = result.StaleAlarms
                    .OrderByDescending(a => a.StaleDuration)
                    .Take(3);
                    
                foreach (var alarm in topStale)
                {
                    Console.WriteLine($"  {alarm.Station}.{alarm.Device}: 陈旧 {alarm.StaleDuration.TotalDays:F1} 天");
                }
            }
            
            Console.WriteLine("\n" + "=".PadRight(50, '='));
        }
        
        /// <summary>
        /// 错误处理示例
        /// </summary>
        public static void ErrorHandlingExample()
        {
            Console.WriteLine("=== 报警分析系统 - 错误处理示例 ===\n");
            
            try
            {
                // 测试无效的连接字符串
                using (var service = new AlarmAnalysisService("invalid connection string"))
                {
                    service.TestDatabaseConnection();
                }
            }
            catch (DataAccessException ex)
            {
                Console.WriteLine($"数据访问异常: {ex.Message}");
                Console.WriteLine($"连接字符串: {ex.ConnectionString}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"其他异常: {ex.Message}");
            }
            
            try
            {
                // 测试无效的时间范围
                using (var service = new AlarmAnalysisService())
                {
                    var invalidStart = DateTime.Now.AddDays(1); // 未来时间
                    var invalidEnd = DateTime.Now;
                    service.LoadAlarmData(invalidStart, invalidEnd);
                }
            }
            catch (ValidationException ex)
            {
                Console.WriteLine($"验证异常: {ex.Message}");
                Console.WriteLine($"验证目标: {ex.ValidationTarget}");
                Console.WriteLine($"验证规则: {ex.ValidationRule}");
            }
            
            try
            {
                // 测试分析空数据
                using (var service = new AlarmAnalysisService())
                {
                    var emptyList = new List<AlarmEvent>();
                    service.PerformCompleteAnalysis(emptyList);
                }
            }
            catch (AnalysisException ex)
            {
                Console.WriteLine($"分析异常: {ex.Message}");
                Console.WriteLine($"分析类型: {ex.AnalysisType}");
                Console.WriteLine($"数据数量: {ex.DataCount}");
            }
        }
    }
}
// {{END_MODIFICATIONS}}
