// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase1-ExceptionHandling"
//   Timestamp: "2024-12-19T11:20:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-Exception-Handling"
//   Quality_Check: "Comprehensive exception hierarchy with proper error categorization."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Runtime.Serialization;

namespace AlarmAnalysis.Common
{
    /// <summary>
    /// 报警分析系统基础异常类
    /// </summary>
    [Serializable]
    public class AlarmAnalysisException : Exception
    {
        public AlarmAnalysisException() : base() { }
        
        public AlarmAnalysisException(string message) : base(message) { }
        
        public AlarmAnalysisException(string message, Exception innerException) : base(message, innerException) { }
        
        protected AlarmAnalysisException(SerializationInfo info, StreamingContext context) : base(info, context) { }
    }
    
    /// <summary>
    /// 数据访问相关异常
    /// </summary>
    [Serializable]
    public class DataAccessException : AlarmAnalysisException
    {
        public string ConnectionString { get; private set; }
        public string SqlCommand { get; private set; }
        
        public DataAccessException() : base() { }
        
        public DataAccessException(string message) : base(message) { }
        
        public DataAccessException(string message, Exception innerException) : base(message, innerException) { }
        
        public DataAccessException(string message, string connectionString, string sqlCommand = null) : base(message)
        {
            ConnectionString = connectionString;
            SqlCommand = sqlCommand;
        }
        
        public DataAccessException(string message, Exception innerException, string connectionString, string sqlCommand = null) 
            : base(message, innerException)
        {
            ConnectionString = connectionString;
            SqlCommand = sqlCommand;
        }
        
        protected DataAccessException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
            ConnectionString = info.GetString(nameof(ConnectionString));
            SqlCommand = info.GetString(nameof(SqlCommand));
        }
        
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue(nameof(ConnectionString), ConnectionString);
            info.AddValue(nameof(SqlCommand), SqlCommand);
        }
    }
    
    /// <summary>
    /// 数据分析相关异常
    /// </summary>
    [Serializable]
    public class AnalysisException : AlarmAnalysisException
    {
        public string AnalysisType { get; private set; }
        public int DataCount { get; private set; }
        
        public AnalysisException() : base() { }
        
        public AnalysisException(string message) : base(message) { }
        
        public AnalysisException(string message, Exception innerException) : base(message, innerException) { }
        
        public AnalysisException(string message, string analysisType, int dataCount = 0) : base(message)
        {
            AnalysisType = analysisType;
            DataCount = dataCount;
        }
        
        public AnalysisException(string message, Exception innerException, string analysisType, int dataCount = 0) 
            : base(message, innerException)
        {
            AnalysisType = analysisType;
            DataCount = dataCount;
        }
        
        protected AnalysisException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
            AnalysisType = info.GetString(nameof(AnalysisType));
            DataCount = info.GetInt32(nameof(DataCount));
        }
        
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue(nameof(AnalysisType), AnalysisType);
            info.AddValue(nameof(DataCount), DataCount);
        }
    }
    
    /// <summary>
    /// 配置相关异常
    /// </summary>
    [Serializable]
    public class ConfigurationException : AlarmAnalysisException
    {
        public string ConfigurationKey { get; private set; }
        public string ConfigurationValue { get; private set; }
        
        public ConfigurationException() : base() { }
        
        public ConfigurationException(string message) : base(message) { }
        
        public ConfigurationException(string message, Exception innerException) : base(message, innerException) { }
        
        public ConfigurationException(string message, string configurationKey, string configurationValue = null) : base(message)
        {
            ConfigurationKey = configurationKey;
            ConfigurationValue = configurationValue;
        }
        
        protected ConfigurationException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
            ConfigurationKey = info.GetString(nameof(ConfigurationKey));
            ConfigurationValue = info.GetString(nameof(ConfigurationValue));
        }
        
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue(nameof(ConfigurationKey), ConfigurationKey);
            info.AddValue(nameof(ConfigurationValue), ConfigurationValue);
        }
    }
    
    /// <summary>
    /// 数据验证相关异常
    /// </summary>
    [Serializable]
    public class ValidationException : AlarmAnalysisException
    {
        public string ValidationTarget { get; private set; }
        public string ValidationRule { get; private set; }
        
        public ValidationException() : base() { }
        
        public ValidationException(string message) : base(message) { }
        
        public ValidationException(string message, Exception innerException) : base(message, innerException) { }
        
        public ValidationException(string message, string validationTarget, string validationRule = null) : base(message)
        {
            ValidationTarget = validationTarget;
            ValidationRule = validationRule;
        }
        
        protected ValidationException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
            ValidationTarget = info.GetString(nameof(ValidationTarget));
            ValidationRule = info.GetString(nameof(ValidationRule));
        }
        
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue(nameof(ValidationTarget), ValidationTarget);
            info.AddValue(nameof(ValidationRule), ValidationRule);
        }
    }
}
// {{END_MODIFICATIONS}}
