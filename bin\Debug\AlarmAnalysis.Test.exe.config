﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>System</value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <connectionStrings>
    <add name="AlarmDatabase" 
         connectionString="Data Source=localhost;Initial Catalog=AlarmHistory;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="TopNCount" value="10" />
    <add key="FlutterTimeWindowSeconds" value="60" />
    <add key="FlutterThreshold" value="3" />
    <add key="FloodTimeWindowMinutes" value="10" />
    <add key="FloodThreshold" value="10" />
  </appSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
</configuration>