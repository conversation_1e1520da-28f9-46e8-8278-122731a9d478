// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase2-BasicAnalysis"
//   Timestamp: "2024-12-19T11:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-LINQ-Usage"
//   Quality_Check: "Comprehensive basic analysis with LINQ optimization and null safety."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using AlarmAnalysis.Models;

namespace AlarmAnalysis.Analysis
{
    /// <summary>
    /// 基础报警分析器 - Phase 2功能实现
    /// 提供频率统计、分布分析和基础KPI计算
    /// </summary>
    public class BasicAlarmAnalyzer
    {
        private readonly int _defaultTopN;
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数，从配置文件读取参数
        /// </summary>
        public BasicAlarmAnalyzer()
        {
            _defaultTopN = int.TryParse(ConfigurationManager.AppSettings["TopNCount"], out int topN) ? topN : 10;
        }
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="defaultTopN">默认Top N数量</param>
        public BasicAlarmAnalyzer(int defaultTopN)
        {
            _defaultTopN = defaultTopN > 0 ? defaultTopN : 10;
        }
        
        #endregion
        
        #region Top N 频率分析
        
        /// <summary>
        /// 获取最频繁的报警消息Top N列表
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量，默认使用配置值</param>
        /// <returns>按频率排序的报警消息统计结果</returns>
        public List<AlarmFrequencyResult> GetTopFrequentAlarmMessages(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new List<AlarmFrequencyResult>();
            
            int n = topN ?? _defaultTopN;
            
            return alarmEvents
                .Where(e => !string.IsNullOrWhiteSpace(e.EventMessage))
                .GroupBy(e => e.EventMessage.Trim())
                .Select(g => new AlarmFrequencyResult
                {
                    ItemName = g.Key,
                    Count = g.Count(),
                    Percentage = (double)g.Count() / alarmEvents.Count * 100,
                    FirstOccurrence = g.Min(e => e.EventDateTime),
                    LastOccurrence = g.Max(e => e.EventDateTime),
                    AverageInterval = CalculateAverageInterval(g.Select(e => e.EventDateTime).OrderBy(d => d).ToList())
                })
                .OrderByDescending(r => r.Count)
                .Take(n)
                .ToList();
        }
        
        /// <summary>
        /// 获取报警最多的设备Top N列表
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>按报警数量排序的设备统计结果</returns>
        public List<AlarmFrequencyResult> GetTopAlarmingDevices(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new List<AlarmFrequencyResult>();
            
            int n = topN ?? _defaultTopN;
            
            return alarmEvents
                .Where(e => !string.IsNullOrWhiteSpace(e.Device))
                .GroupBy(e => e.Device)
                .Select(g => new AlarmFrequencyResult
                {
                    ItemName = g.Key,
                    Count = g.Count(),
                    Percentage = (double)g.Count() / alarmEvents.Count * 100,
                    FirstOccurrence = g.Min(e => e.EventDateTime),
                    LastOccurrence = g.Max(e => e.EventDateTime),
                    UniqueAlarmTypes = g.Select(e => e.EventMessage).Distinct().Count(),
                    AverageInterval = CalculateAverageInterval(g.Select(e => e.EventDateTime).OrderBy(d => d).ToList())
                })
                .OrderByDescending(r => r.Count)
                .Take(n)
                .ToList();
        }
        
        /// <summary>
        /// 获取报警最多的站点Top N列表
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>按报警数量排序的站点统计结果</returns>
        public List<AlarmFrequencyResult> GetTopAlarmingStations(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new List<AlarmFrequencyResult>();
            
            int n = topN ?? _defaultTopN;
            
            return alarmEvents
                .Where(e => !string.IsNullOrWhiteSpace(e.Station))
                .GroupBy(e => e.Station)
                .Select(g => new AlarmFrequencyResult
                {
                    ItemName = g.Key,
                    Count = g.Count(),
                    Percentage = (double)g.Count() / alarmEvents.Count * 100,
                    FirstOccurrence = g.Min(e => e.EventDateTime),
                    LastOccurrence = g.Max(e => e.EventDateTime),
                    UniqueAlarmTypes = g.Select(e => e.EventMessage).Distinct().Count(),
                    UniqueDevices = g.Select(e => e.Device).Distinct().Count(),
                    AverageInterval = CalculateAverageInterval(g.Select(e => e.EventDateTime).OrderBy(d => d).ToList())
                })
                .OrderByDescending(r => r.Count)
                .Take(n)
                .ToList();
        }
        
        #endregion
        
        #region 报警率计算
        
        /// <summary>
        /// 计算总体报警率统计
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>报警率统计结果</returns>
        public AlarmRateResult CalculateAlarmRates(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new AlarmRateResult();
            
            var sortedEvents = alarmEvents.OrderBy(e => e.EventDateTime).ToList();
            var timeSpan = sortedEvents.Last().EventDateTime - sortedEvents.First().EventDateTime;
            
            if (timeSpan.TotalMinutes < 1)
            {
                return new AlarmRateResult
                {
                    TotalAlarms = alarmEvents.Count,
                    TimeSpan = timeSpan,
                    AlarmsPerHour = 0,
                    AlarmsPerDay = 0,
                    PeakAlarmRate = CalculatePeakAlarmRate(alarmEvents)
                };
            }
            
            double totalHours = timeSpan.TotalHours;
            double totalDays = timeSpan.TotalDays;
            
            return new AlarmRateResult
            {
                TotalAlarms = alarmEvents.Count,
                TimeSpan = timeSpan,
                AlarmsPerHour = totalHours > 0 ? alarmEvents.Count / totalHours : 0,
                AlarmsPerDay = totalDays > 0 ? alarmEvents.Count / totalDays : 0,
                PeakAlarmRate = CalculatePeakAlarmRate(alarmEvents),
                AverageInterval = CalculateAverageInterval(sortedEvents.Select(e => e.EventDateTime).ToList())
            };
        }
        
        /// <summary>
        /// 计算峰值报警率（10分钟窗口内的最大报警数）
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="windowMinutes">时间窗口（分钟），默认10分钟</param>
        /// <returns>峰值报警率信息</returns>
        public PeakAlarmRateResult CalculatePeakAlarmRate(List<AlarmEvent> alarmEvents, int windowMinutes = 10)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new PeakAlarmRateResult();
            
            var sortedEvents = alarmEvents.OrderBy(e => e.EventDateTime).ToList();
            var windowSpan = TimeSpan.FromMinutes(windowMinutes);
            
            int maxCount = 0;
            DateTime peakStartTime = sortedEvents.First().EventDateTime;
            DateTime peakEndTime = peakStartTime.Add(windowSpan);
            
            for (int i = 0; i < sortedEvents.Count; i++)
            {
                var windowStart = sortedEvents[i].EventDateTime;
                var windowEnd = windowStart.Add(windowSpan);
                
                int count = sortedEvents.Count(e => e.EventDateTime >= windowStart && e.EventDateTime < windowEnd);
                
                if (count > maxCount)
                {
                    maxCount = count;
                    peakStartTime = windowStart;
                    peakEndTime = windowEnd;
                }
            }
            
            return new PeakAlarmRateResult
            {
                MaxAlarmsInWindow = maxCount,
                WindowSize = windowSpan,
                PeakStartTime = peakStartTime,
                PeakEndTime = peakEndTime,
                AlarmsPerHour = maxCount * (60.0 / windowMinutes)
            };
        }
        
        #endregion
        
        #region 持续与陈旧报警识别
        
        /// <summary>
        /// 识别长期持续报警（Active | Unconfirmed）
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>长期持续报警列表</returns>
        public List<LongStandingAlarmResult> IdentifyLongStandingAlarms(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new List<LongStandingAlarmResult>();
            
            // 按SourceName和EventMessage分组，找出最新状态
            var latestAlarmStates = alarmEvents
                .GroupBy(e => new { e.SourceName, e.EventMessage })
                .Select(g => new
                {
                    Key = g.Key,
                    LatestEvent = g.OrderByDescending(e => e.EventDateTime).First(),
                    FirstOccurrence = g.Min(e => e.EventDateTime),
                    OccurrenceCount = g.Count()
                })
                .Where(x => x.LatestEvent.IsLongStandingAlarm)
                .ToList();
            
            return latestAlarmStates
                .Select(x => new LongStandingAlarmResult
                {
                    SourceName = x.Key.SourceName,
                    Station = x.LatestEvent.Station,
                    Device = x.LatestEvent.Device,
                    EventMessage = x.Key.EventMessage,
                    CurrentState = x.LatestEvent.EventState,
                    FirstOccurrence = x.FirstOccurrence,
                    LastUpdate = x.LatestEvent.EventDateTime,
                    Duration = DateTime.Now - x.FirstOccurrence,
                    OccurrenceCount = x.OccurrenceCount,
                    Priority = x.LatestEvent.Priority,
                    Category = x.LatestEvent.Category
                })
                .OrderByDescending(r => r.Duration)
                .ToList();
        }
        
        /// <summary>
        /// 识别陈旧报警（Inactive | Unconfirmed）
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>陈旧报警列表</returns>
        public List<StaleAlarmResult> IdentifyStaleAlarms(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return new List<StaleAlarmResult>();
            
            // 按SourceName和EventMessage分组，找出最新状态
            var latestAlarmStates = alarmEvents
                .GroupBy(e => new { e.SourceName, e.EventMessage })
                .Select(g => new
                {
                    Key = g.Key,
                    LatestEvent = g.OrderByDescending(e => e.EventDateTime).First(),
                    FirstOccurrence = g.Min(e => e.EventDateTime),
                    OccurrenceCount = g.Count(),
                    StateHistory = g.OrderBy(e => e.EventDateTime).Select(e => e.EventState).ToList()
                })
                .Where(x => x.LatestEvent.IsStaleAlarm)
                .ToList();
            
            return latestAlarmStates
                .Select(x => new StaleAlarmResult
                {
                    SourceName = x.Key.SourceName,
                    Station = x.LatestEvent.Station,
                    Device = x.LatestEvent.Device,
                    EventMessage = x.Key.EventMessage,
                    CurrentState = x.LatestEvent.EventState,
                    FirstOccurrence = x.FirstOccurrence,
                    LastUpdate = x.LatestEvent.EventDateTime,
                    StaleDuration = DateTime.Now - x.LatestEvent.EventDateTime,
                    OccurrenceCount = x.OccurrenceCount,
                    Priority = x.LatestEvent.Priority,
                    Category = x.LatestEvent.Category,
                    StateTransitions = x.StateHistory.Count
                })
                .OrderByDescending(r => r.StaleDuration)
                .ToList();
        }
        
        #endregion
        
        #region 私有辅助方法
        
        /// <summary>
        /// 计算时间间隔的平均值
        /// </summary>
        /// <param name="timestamps">时间戳列表</param>
        /// <returns>平均间隔</returns>
        private static TimeSpan CalculateAverageInterval(List<DateTime> timestamps)
        {
            if (timestamps == null || timestamps.Count < 2)
                return TimeSpan.Zero;
            
            var intervals = new List<TimeSpan>();
            for (int i = 1; i < timestamps.Count; i++)
            {
                intervals.Add(timestamps[i] - timestamps[i - 1]);
            }
            
            if (!intervals.Any())
                return TimeSpan.Zero;
            
            var averageTicks = (long)intervals.Average(i => i.Ticks);
            return new TimeSpan(averageTicks);
        }
        
        #endregion
    }
    
    #region 结果数据模型
    
    /// <summary>
    /// 报警频率分析结果
    /// </summary>
    public class AlarmFrequencyResult
    {
        public string ItemName { get; set; }
        public int Count { get; set; }
        public double Percentage { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastOccurrence { get; set; }
        public TimeSpan AverageInterval { get; set; }
        public int UniqueAlarmTypes { get; set; }
        public int UniqueDevices { get; set; }
    }
    
    /// <summary>
    /// 报警率统计结果
    /// </summary>
    public class AlarmRateResult
    {
        public int TotalAlarms { get; set; }
        public TimeSpan TimeSpan { get; set; }
        public double AlarmsPerHour { get; set; }
        public double AlarmsPerDay { get; set; }
        public PeakAlarmRateResult PeakAlarmRate { get; set; }
        public TimeSpan AverageInterval { get; set; }
    }
    
    /// <summary>
    /// 峰值报警率结果
    /// </summary>
    public class PeakAlarmRateResult
    {
        public int MaxAlarmsInWindow { get; set; }
        public TimeSpan WindowSize { get; set; }
        public DateTime PeakStartTime { get; set; }
        public DateTime PeakEndTime { get; set; }
        public double AlarmsPerHour { get; set; }
    }
    
    /// <summary>
    /// 长期持续报警结果
    /// </summary>
    public class LongStandingAlarmResult
    {
        public string SourceName { get; set; }
        public string Station { get; set; }
        public string Device { get; set; }
        public string EventMessage { get; set; }
        public string CurrentState { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastUpdate { get; set; }
        public TimeSpan Duration { get; set; }
        public int OccurrenceCount { get; set; }
        public int Priority { get; set; }
        public string Category { get; set; }
    }
    
    /// <summary>
    /// 陈旧报警结果
    /// </summary>
    public class StaleAlarmResult
    {
        public string SourceName { get; set; }
        public string Station { get; set; }
        public string Device { get; set; }
        public string EventMessage { get; set; }
        public string CurrentState { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastUpdate { get; set; }
        public TimeSpan StaleDuration { get; set; }
        public int OccurrenceCount { get; set; }
        public int Priority { get; set; }
        public string Category { get; set; }
        public int StateTransitions { get; set; }
    }
    
    #endregion
}
// {{END_MODIFICATIONS}}
