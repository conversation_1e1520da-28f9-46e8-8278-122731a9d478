// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase1-DataModel"
//   Timestamp: "2024-12-19T10:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-DRY"
//   Quality_Check: "Data model follows single responsibility principle with clear property mapping."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace AlarmAnalysis.Models
{
    /// <summary>
    /// 报警事件数据模型，映射SQL Server报警历史表字段
    /// </summary>
    public class AlarmEvent
    {
        #region 数据库字段映射属性
        
        /// <summary>
        /// 报警源名称（原始字段）
        /// </summary>
        public string SourceName { get; set; }
        
        /// <summary>
        /// 报警消息内容
        /// </summary>
        public string EventMessage { get; set; }
        
        /// <summary>
        /// 报警状态（如：Active|Unconfirmed, Inactive|Unconfirmed等）
        /// </summary>
        public string EventState { get; set; }
        
        /// <summary>
        /// 报警发生时间
        /// </summary>
        public DateTime EventDateTime { get; set; }
        
        /// <summary>
        /// 事件唯一标识符
        /// </summary>
        public string EventId { get; set; }
        
        /// <summary>
        /// 事件类型（对应EventType字段）
        /// </summary>
        public string EventType { get; set; }
        
        /// <summary>
        /// 事件详细信息
        /// </summary>
        public string EventDetails { get; set; }
        
        /// <summary>
        /// 严重程度（对应Severity字段，原Priority概念）
        /// </summary>
        public decimal Severity { get; set; }
        
        /// <summary>
        /// 事件持续时间
        /// </summary>
        public double EventDuration { get; set; }
        
        /// <summary>
        /// 事件发生次数
        /// </summary>
        public decimal EventOccurence { get; set; }
        
        /// <summary>
        /// 事件序列号
        /// </summary>
        public decimal EventSequence { get; set; }
        
        /// <summary>
        /// 用户名（对应UserName字段，原AcknowledgedBy概念）
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// 事件注释
        /// </summary>
        public string EventComment { get; set; }
        
        /// <summary>
        /// 源节点
        /// </summary>
        public string SourceNode { get; set; }
        
        /// <summary>
        /// UTC时间
        /// </summary>
        public DateTime? EventDateTimeUtc { get; set; }
        
        /// <summary>
        /// 冗余同步时间
        /// </summary>
        public DateTime? RedundancySyncTime { get; set; }
        
        #endregion
        
        #region 解析后的扩展属性
        
        /// <summary>
        /// 从SourceName解析出的站点信息
        /// </summary>
        public string Station { get; private set; }
        
        /// <summary>
        /// 从SourceName解析出的设备信息
        /// </summary>
        public string Device { get; private set; }
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AlarmEvent()
        {
        }
        
        /// <summary>
        /// 带SourceName解析的构造函数
        /// </summary>
        /// <param name="sourceName">源名称</param>
        public AlarmEvent(string sourceName)
        {
            SourceName = sourceName;
            ParseSourceName();
        }
        
        #endregion
        
        #region SourceName解析逻辑
        
        /// <summary>
        /// 解析SourceName，提取Station和Device信息
        /// 支持多种常见的命名模式：
        /// - Station/Device/Source (如: PingShanStation/FT_2131/Source)
        /// - Station.Device
        /// - Station_Device
        /// - Station-Device
        /// - StationDevice（通过大小写分割）
        /// </summary>
        private void ParseSourceName()
        {
            if (string.IsNullOrWhiteSpace(SourceName))
            {
                Station = "Unknown";
                Device = "Unknown";
                return;
            }
            
            try
            {
                // 模式1: 使用斜杠分隔 (Station/Device/Source) - 优先处理，这是UFUAAuditLogItem的常见格式
                if (SourceName.Contains("/"))
                {
                    var parts = SourceName.Split('/');
                    if (parts.Length >= 2)
                    {
                        Station = parts[0].Trim();
                        Device = parts[1].Trim();
                        // 如果有第三部分(如Source)，可以忽略或作为设备的一部分
                    }
                    else
                    {
                        Station = parts[0].Trim();
                        Device = "Unknown";
                    }
                }
                // 模式2: 使用点号分隔 (Station.Device)
                else if (SourceName.Contains("."))
                {
                    var parts = SourceName.Split('.');
                    Station = parts[0].Trim();
                    Device = parts.Length > 1 ? string.Join(".", parts, 1, parts.Length - 1).Trim() : "Unknown";
                }
                // 模式3: 使用下划线分隔 (Station_Device)
                else if (SourceName.Contains("_"))
                {
                    var parts = SourceName.Split('_');
                    Station = parts[0].Trim();
                    Device = parts.Length > 1 ? string.Join("_", parts, 1, parts.Length - 1).Trim() : "Unknown";
                }
                // 模式4: 使用连字符分隔 (Station-Device)
                else if (SourceName.Contains("-"))
                {
                    var parts = SourceName.Split('-');
                    Station = parts[0].Trim();
                    Device = parts.Length > 1 ? string.Join("-", parts, 1, parts.Length - 1).Trim() : "Unknown";
                }
                // 模式5: 使用正则表达式匹配驼峰命名或数字分割
                else
                {
                    // 尝试通过大写字母或数字分割
                    var regex = new Regex(@"([A-Z][a-z]*|\d+|[a-z]+)");
                    var matches = regex.Matches(SourceName);
                    
                    if (matches.Count >= 2)
                    {
                        Station = matches[0].Value;
                        Device = string.Join("", matches.Cast<Match>().Skip(1).Select(m => m.Value));
                    }
                    else
                    {
                        // 如果无法解析，将整个名称作为设备名，站点设为Unknown
                        Station = "Unknown";
                        Device = SourceName.Trim();
                    }
                }
                
                // 确保不为空
                if (string.IsNullOrWhiteSpace(Station)) Station = "Unknown";
                if (string.IsNullOrWhiteSpace(Device)) Device = "Unknown";
            }
            catch (Exception)
            {
                // 解析失败时的默认值
                Station = "Unknown";
                Device = SourceName?.Trim() ?? "Unknown";
            }
        }
        
        /// <summary>
        /// 手动设置SourceName并触发解析
        /// </summary>
        /// <param name="sourceName">源名称</param>
        public void SetSourceName(string sourceName)
        {
            SourceName = sourceName;
            ParseSourceName();
        }
        
        #endregion
        
        #region 状态判断辅助方法
        
        /// <summary>
        /// 判断是否为活动报警
        /// </summary>
        public bool IsActive => EventState?.Contains("Active") == true;
        
        /// <summary>
        /// 判断是否为未确认报警（适配实际数据格式 "Unacknowledged"）
        /// </summary>
        public bool IsUnacknowledged => EventState?.Contains("Unacknowledged") == true;
        
        /// <summary>
        /// 判断是否为已确认报警
        /// </summary>
        public bool IsAcknowledged => EventState?.Contains("Acknowledged") == true && !IsUnacknowledged;
        
        /// <summary>
        /// 判断是否为非活动报警
        /// </summary>
        public bool IsInactive => EventState?.Contains("Inactive") == true;
        
        /// <summary>
        /// 判断是否为长期持续报警（Active | Unacknowledged）
        /// </summary>
        public bool IsLongStandingAlarm => IsActive && IsUnacknowledged;
        
        /// <summary>
        /// 判断是否为陈旧报警（Inactive | Unacknowledged）
        /// </summary>
        public bool IsStaleAlarm => IsInactive && IsUnacknowledged;
        
        /// <summary>
        /// 获取报警优先级（基于Severity数值）
        /// </summary>
        public int Priority 
        {
            get
            {
                if (Severity >= 100) return 1;  // 高优先级
                if (Severity >= 50) return 2;   // 中优先级
                return 3;                       // 低优先级
            }
        }
        
        /// <summary>
        /// 获取报警类别（从EventType解析）
        /// </summary>
        public string Category => EventType?.StartsWith("i=") == true ? "OPC_UA_Event" : EventType ?? "Unknown";
        
        #endregion
        
        #region 重写方法
        
        public override string ToString()
        {
            return $"[{EventDateTime:yyyy-MM-dd HH:mm:ss}] {Station}.{Device}: {EventMessage} ({EventState})";
        }
        
        public override bool Equals(object obj)
        {
            if (obj is AlarmEvent other)
            {
                return SourceName == other.SourceName && 
                       EventMessage == other.EventMessage && 
                       EventDateTime == other.EventDateTime;
            }
            return false;
        }
        
        public override int GetHashCode()
        {
            return (SourceName?.GetHashCode() ?? 0) ^ 
                   (EventMessage?.GetHashCode() ?? 0) ^ 
                   EventDateTime.GetHashCode();
        }
        
        #endregion
    }
}
// {{END_MODIFICATIONS}}
