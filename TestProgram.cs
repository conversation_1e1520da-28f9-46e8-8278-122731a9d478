// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Testing-Core-Functions"
//   Timestamp: "2024-12-19T12:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-Testing"
//   Quality_Check: "Standalone test program for core functionality verification."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using AlarmAnalysis.Models;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Common;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// 测试程序 - 验证核心功能是否正常工作
    /// 不依赖DevExpress和数据库连接
    /// </summary>
    public class TestProgram
    {
        public static void Main(string[] args)
        {
            // 设置控制台编码为UTF-8以正确显示中文
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
            
            Console.WriteLine("=== 报警分析系统 - 核心功能测试 ===\n");
            
            try
            {
                // 测试1: 数据模型功能
                TestAlarmEventModel();
                
                // 测试2: 配置管理功能
                TestConfigurationHelper();
                
                // 测试3: 分析引擎功能
                TestBasicAlarmAnalyzer();
                
                // 测试4: 异常处理功能
                TestExceptionHandling();
                
                Console.WriteLine("\n=== 所有测试完成 ===");
                Console.WriteLine("核心功能验证通过！✅");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 测试AlarmEvent数据模型
        /// </summary>
        private static void TestAlarmEventModel()
        {
            Console.WriteLine("1. 测试 AlarmEvent 数据模型...");
            
            // 测试不同的SourceName解析模式
            var testCases = new[]
            {
                "Station1.Device1",
                "Station2_Device2", 
                "Station3-Device3",
                "StationADeviceB",
                "ComplexStation.SubSystem.Device1"
            };
            
            foreach (var sourceName in testCases)
            {
                var alarm = new AlarmEvent(sourceName)
                {
                    EventMessage = "Test Alarm Message",
                    EventState = "Active|Unconfirmed",
                    EventDateTime = DateTime.Now,
                    Priority = 1
                };
                
                Console.WriteLine($"  SourceName: {sourceName} -> Station: {alarm.Station}, Device: {alarm.Device}");
                Console.WriteLine($"    IsLongStanding: {alarm.IsLongStandingAlarm}");
            }
            
            Console.WriteLine("  ✅ AlarmEvent 模型测试通过\n");
        }
        
        /// <summary>
        /// 测试配置管理功能
        /// </summary>
        private static void TestConfigurationHelper()
        {
            Console.WriteLine("2. 测试配置管理功能...");
            
            try
            {
                // 测试配置读取（使用默认值）
                var topN = ConfigurationHelper.GetIntValue("TopNCount", 10);
                var timeout = ConfigurationHelper.GetIntValue("DatabaseTimeoutSeconds", 30);
                var batchSize = ConfigurationHelper.GetIntValue("BatchSize", 1000);
                
                Console.WriteLine($"  TopN: {topN}");
                Console.WriteLine($"  Timeout: {timeout}");
                Console.WriteLine($"  BatchSize: {batchSize}");
                
                // 测试配置验证
                var validation = ConfigurationHelper.ValidateConfiguration();
                Console.WriteLine($"  配置验证结果: {(validation.IsValid ? "有效" : "无效")}");
                if (validation.HasWarnings)
                {
                    Console.WriteLine($"  警告数量: {validation.Warnings.Count}");
                }
                
                Console.WriteLine("  ✅ 配置管理测试通过\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ⚠️ 配置管理测试异常（预期的）: {ex.Message}\n");
            }
        }
        
        /// <summary>
        /// 测试基础分析引擎
        /// </summary>
        private static void TestBasicAlarmAnalyzer()
        {
            Console.WriteLine("3. 测试基础分析引擎...");
            
            // 创建测试数据
            var testAlarms = CreateTestAlarmData();
            Console.WriteLine($"  创建了 {testAlarms.Count} 条测试报警数据");
            
            var analyzer = new BasicAlarmAnalyzer(5); // Top 5
            
            // 测试Top N分析
            var topMessages = analyzer.GetTopFrequentAlarmMessages(testAlarms);
            Console.WriteLine($"  Top 报警消息: {topMessages.Count} 项");
            foreach (var msg in topMessages)
            {
                Console.WriteLine($"    {msg.ItemName}: {msg.Count} 次 ({msg.Percentage:F1}%)");
            }
            
            var topDevices = analyzer.GetTopAlarmingDevices(testAlarms);
            Console.WriteLine($"  Top 报警设备: {topDevices.Count} 项");
            
            var topStations = analyzer.GetTopAlarmingStations(testAlarms);
            Console.WriteLine($"  Top 报警站点: {topStations.Count} 项");
            
            // 测试报警率计算
            var rates = analyzer.CalculateAlarmRates(testAlarms);
            Console.WriteLine($"  总报警数: {rates.TotalAlarms}");
            Console.WriteLine($"  平均报警率: {rates.AlarmsPerHour:F2} 次/小时");
            Console.WriteLine($"  峰值报警率: {rates.PeakAlarmRate.MaxAlarmsInWindow} 次/{rates.PeakAlarmRate.WindowSize.TotalMinutes:F0}分钟");
            
            // 测试状态分析
            var longStanding = analyzer.IdentifyLongStandingAlarms(testAlarms);
            var staleAlarms = analyzer.IdentifyStaleAlarms(testAlarms);
            Console.WriteLine($"  长期持续报警: {longStanding.Count} 项");
            Console.WriteLine($"  陈旧报警: {staleAlarms.Count} 项");
            
            Console.WriteLine("  ✅ 基础分析引擎测试通过\n");
        }
        
        /// <summary>
        /// 测试异常处理功能
        /// </summary>
        private static void TestExceptionHandling()
        {
            Console.WriteLine("4. 测试异常处理功能...");
            
            try
            {
                throw new DataAccessException("测试数据访问异常", "test connection string", "SELECT * FROM TestTable");
            }
            catch (DataAccessException ex)
            {
                Console.WriteLine($"  捕获DataAccessException: {ex.Message}");
                Console.WriteLine($"  连接字符串: {ex.ConnectionString}");
                Console.WriteLine($"  SQL命令: {ex.SqlCommand}");
            }
            
            try
            {
                throw new AnalysisException("测试分析异常", "TestAnalysis", 100);
            }
            catch (AnalysisException ex)
            {
                Console.WriteLine($"  捕获AnalysisException: {ex.Message}");
                Console.WriteLine($"  分析类型: {ex.AnalysisType}");
                Console.WriteLine($"  数据数量: {ex.DataCount}");
            }
            
            try
            {
                throw new ValidationException("测试验证异常", "TestField", "必须大于0");
            }
            catch (ValidationException ex)
            {
                Console.WriteLine($"  捕获ValidationException: {ex.Message}");
                Console.WriteLine($"  验证目标: {ex.ValidationTarget}");
                Console.WriteLine($"  验证规则: {ex.ValidationRule}");
            }
            
            Console.WriteLine("  ✅ 异常处理测试通过\n");
        }
        
        /// <summary>
        /// 创建测试报警数据
        /// </summary>
        private static List<AlarmEvent> CreateTestAlarmData()
        {
            var alarms = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-1);
            
            var stations = new[] { "Station1", "Station2", "Station3" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4" };
            var messages = new[] 
            {
                "Temperature High Alarm",
                "Pressure Low Alarm", 
                "Communication Failure",
                "Power Supply Fault",
                "Sensor Malfunction"
            };
            var states = new[]
            {
                "Active|Unconfirmed",
                "Inactive|Unconfirmed", 
                "Active|Confirmed",
                "Inactive|Confirmed"
            };
            
            // 生成100条测试数据
            for (int i = 0; i < 100; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var sourceName = $"{station}.{device}";
                
                var alarm = new AlarmEvent(sourceName)
                {
                    EventMessage = messages[random.Next(messages.Length)],
                    EventState = states[random.Next(states.Length)],
                    EventDateTime = baseTime.AddMinutes(random.Next(0, 1440)), // 24小时内随机时间
                    Priority = random.Next(1, 4),
                    Category = "Test"
                };
                
                alarms.Add(alarm);
            }
            
            // 按时间排序
            alarms.Sort((a, b) => a.EventDateTime.CompareTo(b.EventDateTime));
            
            return alarms;
        }
    }
}
// {{END_MODIFICATIONS}}
