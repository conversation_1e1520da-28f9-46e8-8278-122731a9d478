/*
 Navicat Premium Dump SQL

 Source Server         : local
 Source Server Type    : SQL Server
 Source Server Version : 16001000 (16.00.1000)
 Source Host           : .:1433
 Source Catalog        : DaPeng_IOServer
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 16001000 (16.00.1000)
 File Encoding         : 65001

 Date: 23/08/2025 17:10:13
*/


-- ----------------------------
-- Table structure for UFUAAuditLogItem
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[UFUAAuditLogItem]') AND type IN ('U'))
	DROP TABLE [dbo].[UFUAAuditLogItem]
GO

CREATE TABLE [dbo].[UFUAAuditLogItem] (
  [OID] int  IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  [EventId] uniqueidentifier  NULL,
  [EventType] nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [SourceNode] nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [SourceName] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [EventDateTime] datetime  NULL,
  [EventDateTimeUtc] datetime  NULL,
  [EventMessage] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [EventDetails] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [EventState] nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [Severity] numeric(5)  NULL,
  [EventComment] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [UserName] nvarchar(256) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [EventDuration] float(53)  NULL,
  [EventOccurence] numeric(20)  NULL,
  [EventSequence] numeric(20)  NULL,
  [RedundancySyncTime] datetime  NULL,
  [OptimisticLockField] int  NULL
)
GO

ALTER TABLE [dbo].[UFUAAuditLogItem] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for UFUAAuditLogItem
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[UFUAAuditLogItem]', RESEED, 26843)
GO


-- ----------------------------
-- Indexes structure for table UFUAAuditLogItem
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [iEventId_UFUAAuditLogItem]
ON [dbo].[UFUAAuditLogItem] (
  [EventId] ASC
)
GO

CREATE NONCLUSTERED INDEX [iEventDateTime_UFUAAuditLogItem]
ON [dbo].[UFUAAuditLogItem] (
  [EventDateTime] ASC
)
GO

CREATE NONCLUSTERED INDEX [iEventDateTimeUtc_UFUAAuditLogItem]
ON [dbo].[UFUAAuditLogItem] (
  [EventDateTimeUtc] ASC
)
GO

CREATE NONCLUSTERED INDEX [iRedundancySyncTime_UFUAAuditLogItem]
ON [dbo].[UFUAAuditLogItem] (
  [RedundancySyncTime] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table UFUAAuditLogItem
-- ----------------------------
ALTER TABLE [dbo].[UFUAAuditLogItem] ADD CONSTRAINT [PK_UFUAAuditLogItem] PRIMARY KEY CLUSTERED ([OID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

