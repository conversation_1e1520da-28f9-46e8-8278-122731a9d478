### **项目：报警历史数据分析工具**

**目标**：开发一个桌面应用程序，用于深度分析从SQL Server数据库导出的报警历史数据，提供可操作的运营洞察，并以表格和图表形式展示结果。

**目标框架**：.NET Framework 4.8

---

### **Phase 1: 项目基础架构与数据接入层**

* **任务目标**: 搭建项目骨架，并实现一个高效、可靠的数据读取和建模模块。
* **关键功能需求**:
    1.  **项目创建**:
        * 创建一个 WPF 或 Windows Forms 项目，目标框架设置为 .NET Framework 4.8。
    2.  **数据建模**:
        * 创建一个名为 `AlarmEvent` 的C#类，其属性应精确映射报警历史表的各个字段（如 `SourceName`, `EventMessage`, `EventState`, `EventDateTime`）。
        * 在 `AlarmEvent` 类中额外添加 `Station` 和 `Device` 属性，用于存储从 `SourceName` 解析出的站点和设备信息。
    3.  **高性能数据读取**:
        * 实现一个数据访问模块，专门负责从 SQL Server 高效检索报警数据。
        * 该模块必须使用 **ADO.NET** 和 `SqlDataReader` 来流式读取数据，以确保处理大规模数据集时的低内存占用和高性能。
        * 执行的 SQL 查询语句**必须**包含 `ORDER BY EventDateTime ASC` 子句，确保数据在进入分析引擎前已按时间正确排序。
        * 在数据读取循环中，将每一行记录实例化为一个 `AlarmEvent` 对象，并完成 `SourceName` 的解析工作。
* **建议采用的模块/库**:
    * `System.Data.SqlClient` (或 `Microsoft.Data.SqlClient` NuGet包) 用于数据库连接。
* **健壮性要求**:
    * 数据库连接字符串应从配置文件（App.config）读取，而不是硬编码。
    * 必须包含完整的异常处理逻辑，能够捕获并清晰报告数据库连接失败、查询执行错误或数据格式不匹配等问题。

### **Phase 2: 核心分析引擎 - 基础频率与分布指标**

* **任务目标**: 基于加载的数据，实现第一层级的静态、基于计数的分析功能。
* **关键功能需求**:
    1.  **Top N 最频繁报警**:
        * 按 `EventMessage` 分组统计，输出最频繁报警的Top N列表（N可配置）及其发生次数。
        * 按解析出的 `Device` 分组统计，输出报警最多的Top N设备列表。
        * 按解析出的 `Station` 分组统计，输出报警最多的Top N站点/区域列表。
    2.  **报警率计算**:
        * 计算分析时间范围内的总报警率（例如，平均每小时/每天的报警数）。
        * 实现峰值报警率计算：找出任意10分钟窗口内的最大报警数量。
    3.  **持续与陈旧报警识别**:
        * 识别并列出所有“长期持续报警”（Long-Standing Alarms），即当前最新状态为 `Active | Unconfirmed` 的报警。
        * 识别并列出所有“陈旧报警”（Stale Alarms），即当前最新状态为 `Inactive | Unconfirmed` 的报警。
* **建议采用的模块/库**:
    * **LINQ**: 大量使用 `GroupBy`, `Count`, `OrderByDescending`, `Select` 等方法进行数据聚合与查询。
* **健壮性要求**:
    * 所有分析功能都应能正确处理空数据集或不包含有效报警事件的数据集，避免抛出空引用异常。
    * Top N 功能中的 N 值应有合理的默认值和范围限制。

### **Phase 3: 核心分析引擎 - 时间与行为分析**

* **任务目标**: 引入时间维度，实现基于报警生命周期的状态化分析。
* **关键功能需求**:
    1.  **报警生命周期重构**:
        * 实现一个核心逻辑，用于追踪每一个独立报警实例的完整生命周期。
        * 遍历按时间排序的 `AlarmEvent` 列表，使用一个 `Dictionary` 来存储每个报警实例的状态变迁（键可以是 `SourceName`+`EventMessage`+初始触发时间戳的组合），记录其进入各个关键状态的时间点。
    2.  **响应KPI计算**:
        * 基于重构的生命周期数据，计算每个报警实例的“确认前时长 (TTA)”。
        * 计算每个报警实例的“解决前时长 (TTR)”。
        * 提供这些时长的统计结果，包括平均值、最大值以及分布情况（为后续图表做准备）。
    3.  **抖动与瞬时报警检测**:
        * 实现一个算法，用于识别在短时间窗口内（可配置，如60秒）反复激活（状态变为Active）超过一定次数（可配置，如3次）的“抖动报警”。
        * 识别出“瞬时报警”，即从 `Active | Unacknowledged` 直接变为 `Inactive | Unacknowledged` 的报警。
* **建议采用的模块/库**:
    * `System.Collections.Generic.Dictionary<TKey, TValue>` 用于追踪报警生命周期。
    * `System.TimeSpan` 用于精确计算和存储时间差。
* **健壮性要求**:
    * 生命周期重构逻辑必须能正确处理数据缺失（例如，只有激活记录没有后续状态记录）的情况。
    * 时长计算应能处理各种状态转换顺序，包括非典型的瞬时报警。

### **Phase 4: 高级关联与序列分析**

* **任务目标**: 实现复杂的关联分析算法，挖掘报警之间的深层关系。
* **关键功能需求**:
    1.  **报警风暴分析**:
        * 使用“滑动窗口”算法（例如，一个队列）来识别报警风暴。
        * 当一个10分钟（可配置）的窗口内新产生的报警数量超过10个（可配置）时，记录为一次报警风暴事件。
        * 对于每一次识别出的报警风暴，需记录其起止时间、持续时长、期间的峰值报警率以及**最重要的“首出报警”（First-Out Alarm）**。
    2.  **序列模式挖掘 (简化版)**:
        * 实现一个算法，用于发现强关联的报警序列（如“报警A发生后，总是在30秒内发生报警B”）。
        * 算法应能计算有序报警对（A -> B）的支持度（发生次数）和置信度（条件概率 P(B|A)）。
        * 输出满足预设阈值（如置信度 > 80% 且发生次数 > 50次）的强关联规则列表。
        * **重要**: 此分析模块的输入数据，应该是**经过抖动报警过滤或合并后**的数据，以避免无意义的自身关联。
* **建议采用的模块/库**:
    * 将每个高级分析算法封装在独立的类中，如 `AlarmFloodAnalyzer` 和 `SequencePatternMiner`，以保持代码的模块化和清晰度。
* **健壮性要求**:
    * 算法中的时间窗口和阈值参数应该是可配置的。
    * 应能处理分析时长过短，不足以形成有效模式的情况。

### **Phase 5: 用户界面、可视化与报告**

* **任务目标**: 提供一个用户友好的界面来运行分析并直观地展示结果。
* **关键功能需求**:
    1.  **主界面**:
        * 提供输入字段，用于配置数据库连接、选择分析的起止时间。
        * 提供按钮来触发不同类别的分析。
        * 提供状态显示，告知用户当前分析进度（例如，正在读取数据、正在计算...）。
    2.  **结果展示**:
        * 使用表格（如 `DataGrid`）来展示列表式的分析结果（如Top N列表、报警风暴事件列表等）。
    3.  **数据可视化**:
        * **柱状图**: 用于展示Top N报警频率的排名。
        * **折线图**: 用于展示报警率随时间的变化趋势（例如，按小时或按天的报警数）。
        * **直方图**: 用于展示 TTA 和 TTR 的分布情况，揭示响应时间的集中趋势与异常值。
    4.  **报告导出**:
        * 提供功能，将所有表格形式的分析结果导出为CSV或文本文件。
* **建议采用的模块/库**:
    * **ScottPlot** (`ScottPlot.WPF` 或 `ScottPlot.WinForms` NuGet包) 用于实现所有数据可视化图表。
* **健壮性要求**:
    * UI在执行耗时长的分析任务时不应冻结，应使用后台线程（如 `BackgroundWorker` 或 `async/await`）来执行分析。
    * 图表应能正确处理大量数据点，并提供基本的交互功能（如缩放、平移）。
    * 当分析结果为空时，应向用户显示清晰的提示信息，而不是空白的图表或表格。